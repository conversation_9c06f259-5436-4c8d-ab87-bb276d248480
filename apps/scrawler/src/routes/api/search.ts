import { uploadFileByBuffer } from '@/api/aliyun'
import Tiktok<PERSON>pi from '@/api/tiktok.ts'
import { handleUnknownError } from '@/common/errorHandler'
import { throwError } from '@/common/errors/statusCodes'
import {
  StatusCodes,
  errorResponse,
  notFoundResponse,
  serverErrorResponse,
  successResponse,
} from '@/common/response/response'
import { HOST } from '@/config/env'
import { LongCrawlerExportColumns } from '@/config/exportColumns'
import { FullResponseSchema } from '@/config/swagger'
import { QuotaCost } from '@/enums/QuotaCost'
import InstagramRapidApiV3 from '@/lib/instagramRapidApi.v3'
import { withAuth } from '@/middlewares/auth'
import { checkQuota, dynamicCheckQuota } from '@/middlewares/quota'
import { AudienceService } from '@/services/audience.service'
import { DynamicQuotaService } from '@/services/dynamicQuota.service'
import { commonStyle, exportTaskResult } from '@/services/export.service'
import InstagramService from '@/services/instagram'
import YoutubeService from '@/services/youtube'
import YoutubeApi from '@/api/youtube'
import { MembershipService } from '@/services/membership.service'
import { getProjectCandidatesWithPostsByTaskType } from '@/services/project.ts'
import { similarSearchForAllPlatforms } from '@/services/similar'
import TaskService from '@/services/task.ts'
import { TimezoneService } from '@/services/timezone.service'
import {
  TtFollowersSimilarRequest,
  TtFollowingListRequest,
  TtHashTagBreakRequest,
  TtSearchInputBreakRequest,
} from '@/types/request/search.ts'
import {
  AudienceAnalysisTaskParams,
  InsLongCrawlerProgress,
  InsLongCrawlerTaskParams,
  TtFollowersSimilarTaskParams,
  TtFollowingListTaskParams,
  TtHashTagBreakTaskParams,
  TtSearchInputBreakTaskParams,
  YtbLongCrawlerProgress,
  YtbLongCrawlerTaskParams,
} from '@/types/task.ts'
import {
  InstagramPriorityQueue,
  InstagramPriorityUser,
  PriorityQueueFactory,
  YoutubePriorityQueue,
  YoutubePriorityUser,
  calculateUserPriority,
  QueueUser,
} from '@/utils/PriorityQueue'
import { exportToExcel } from '@/utils/excel'
import {
  $Enums,
  KolPlatform,
  ProjectKolAttitude,
  QuotaType,
  SimilarChannelTaskStatus,
  TaskType,
  prisma,
} from '@repo/database'
import assert, { AssertionError } from 'assert'
import { FastifyPluginAsync, FastifyRequest } from 'fastify'
import {
  LongCrawlerExportRequest,
  LongCrawlerExportResponseSchema,
  LongCrawlerExportSchema,
  LongCrawlerRateBodySchema,
  LongCrawlerRateDataSchema,
  LongCrawlerRateParamsSchema,
  LongCrawlerRequest,
  LongCrawlerRequestSchema,
  LongCrawlerTaskResponseSchema,
  LongCrawlerUsersParams,
  LongCrawlerUsersParamsSchema,
  LongCrawlerUsersQuery,
  LongCrawlerUsersQuerySchema,
  LongCrawlerUsersResponseSchema,
  TaskStatusQueryParams,
  TaskStatusQueryParamsSchema,
  TaskStatusResponseSchema,
  UpdateLongCrawlerParams,
  UpdateLongCrawlerParamsResponseSchema,
  UpdateLongCrawlerParamsSchema,
  WebExportParams,
  WebExportParamsSchema,
} from '../schemas/search'
import TaskReason = $Enums.TaskReason
import { TaskTypeToParamsMap } from '@/types/taskParams'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  // tiktok 的 hashTag爆破
  fastify.post<{ Body: TtHashTagBreakRequest }>(
    '/hashTag',
    withAuth(
      dynamicCheckQuota(
        DynamicQuotaService.calculateHashTagSearchQuota,
        QuotaType.TT_HASH_TAG_BREAK,
      ),
    ),
    async (req: FastifyRequest<{ Body: TtHashTagBreakRequest }>, reply) => {
      try {
        const { projectId, platform, reason, maxVideoCount, currentVideoCount } = req.body
        let { tag } = req.body
        tag = tag.replace('#', '')
        tag = tag.trim()
        tag = tag.replace(/ /g, '')
        tag = tag.toLowerCase()
        const user = (req as any).user
        assert(tag, 'tag is required and cannot be empty!')

        const hashTagInfo = await TiktokApi.getInstance().getHashTag(tag)
        assert(hashTagInfo?.id, 'tag is not found!')

        let cursor = 0
        if (reason !== TaskReason.SEARCH) {
          const projectCandidate = await prisma.projectCandidate.findUnique({
            where: {
              projectId_type: {
                projectId,
                type: TaskType.HASH_TAG_BREAK,
              },
            },
          })
          const meta = (projectCandidate?.meta as any) || {}
          cursor = meta.cursor || 0
        }

        const params: TtHashTagBreakTaskParams = {
          projectId,
          platform,
          tag,
          cursor,
          maxVideoCount,
          currentVideoCount,
        }
        const task = await TaskService.getInstance().createTask(
          projectId,
          params,
          user.id,
          reason || TaskReason.SEARCH,
          TaskType.HASH_TAG_BREAK,
        )
        return reply.status(200).send(successResponse(task))
      } catch (error) {
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  // tiktok 的关键词搜索爆破
  fastify.post<{ Body: TtSearchInputBreakRequest }>(
    '/searchInput',
    withAuth(
      dynamicCheckQuota(
        DynamicQuotaService.calculateInputSearchQuota,
        QuotaType.TT_SEARCH_INPUT_BREAK,
      ),
    ),
    async (req: FastifyRequest<{ Body: TtSearchInputBreakRequest }>, reply) => {
      try {
        const { projectId, platform, searchInput, reason, sortType, publishTimeType } = req.body
        const { maxVideoCount } = req.body ?? 100
        const user = (req as any).user
        if (!projectId || !platform || !searchInput) {
          return reply
            .status(400)
            .send(
              errorResponse(
                StatusCodes.BAD_REQUEST,
                'BAD_REQUEST',
                'projectId,platform is required!',
              ),
            )
        }
        if (![0, 1, 3].includes(sortType)) {
          return reply
            .status(400)
            .send(errorResponse(StatusCodes.BAD_REQUEST, 'BAD_REQUEST', 'sortType is invalid!'))
        }

        if (![0, 1, 7, 30, 90, 180].includes(publishTimeType)) {
          return reply
            .status(400)
            .send(
              errorResponse(StatusCodes.BAD_REQUEST, 'BAD_REQUEST', 'publishTimeType is invalid!'),
            )
        }

        let cursor = 0
        if (reason !== TaskReason.SEARCH) {
          const projectCandidate = await prisma.projectCandidate.findUnique({
            where: {
              projectId_type: {
                projectId,
                type: TaskType.SEARCH_INPUT_BREAK,
              },
            },
          })
          const meta = (projectCandidate?.meta as any) || {}
          cursor = meta.cursor || 0
        }

        const taskParams: TtSearchInputBreakTaskParams = {
          projectId,
          platform,
          searchInput,
          sortType,
          publishTimeType,
          cursor,
          maxVideoCount,
        }
        const task = await TaskService.getInstance().createTask(
          projectId,
          taskParams,
          user.id,
          reason || TaskReason.SEARCH,
          TaskType.SEARCH_INPUT_BREAK,
        )
        return reply.status(200).send(successResponse(task))
      } catch (error) {
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )
  /**
   * 通过粉丝列表获取相似用户(没有翻页)
   */
  fastify.post<{ Body: TtFollowersSimilarRequest }>(
    '/followersSimilar',
    withAuth(checkQuota(QuotaCost.TT_FOLLOWERS_SIMILAR, QuotaType.TT_FOLLOWERS_SIMILAR)),
    async (req: FastifyRequest<{ Body: TtFollowersSimilarRequest }>, reply) => {
      const { projectId, platform, uniqueId, excludeWords, reason } =
        req.body as TtFollowersSimilarRequest
      const user = (req as any).user
      if (!projectId || !platform || !uniqueId) {
        return reply
          .status(400)
          .send(serverErrorResponse('projectId,platform,uniqueId is required'))
      }
      try {
        const excludeWordsArray = excludeWords ? excludeWords.split(',') : undefined
        const taskParams: TtFollowersSimilarTaskParams = {
          projectId,
          platform,
          uniqueId,
          excludeWords: excludeWordsArray,
        }
        const task = await TaskService.getInstance().createTask(
          projectId,
          taskParams,
          user.id,
          reason || TaskReason.SEARCH,
          TaskType.FOLLOWERS_SIMILAR,
        )
        return reply.status(200).send(successResponse(task, 'success'))
      } catch (error) {
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  /**
   * 获取用户关注列表
   */
  fastify.post(
    '/followingList',
    withAuth(checkQuota(QuotaCost.TT_FOLLOWING_LIST, QuotaType.TT_FOLLOWING_LIST)),
    async (req: FastifyRequest<{ Body: TtFollowingListRequest }>, reply) => {
      const { projectId, platform, uniqueId, reason } = req.body as TtFollowingListRequest
      const user = (req as any).user
      if (!projectId || !platform || !uniqueId) {
        return reply
          .status(400)
          .send(serverErrorResponse('projectId,platform,uniqueId is required'))
      }
      try {
        const userDetail = await TiktokApi.getInstance().getUserDetail({ unique_id: uniqueId })
        if (!userDetail || !userDetail.user || !userDetail.user.id) {
          throw new Error('该博主系统无法解析')
        }
        const followingResponse = await TiktokApi.getInstance().getUserFollowing(
          userDetail.user.id,
          1,
          0,
        )
        if (!followingResponse) {
          throw new Error('该博主的关注列表未开放')
        }
        let time = 0
        if (reason !== TaskReason.SEARCH) {
          const projectCandidate = await prisma.projectCandidate.findUnique({
            where: {
              projectId_type: {
                projectId,
                type: TaskType.FOLLOWING_LIST,
              },
            },
          })
          const meta = (projectCandidate?.meta as any) || {}
          time = meta.time || 0
        }

        const taskParams: TtFollowingListTaskParams = {
          projectId,
          platform,
          uniqueId,
          userId: userDetail.user.id,
          currentTime: time,
          maxCount: 100,
          currentCount: 0,
        }

        console.log('taskParams', taskParams)
        const task = await TaskService.getInstance().createTask(
          projectId,
          taskParams,
          user.id,
          reason || TaskReason.SEARCH,
          TaskType.FOLLOWING_LIST,
        )
        return reply.status(200).send(successResponse(task, 'success'))
      } catch (error) {
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  /**
   *  三个平台的受众分析任务
   */
  fastify.post(
    '/audienceAnalysis',
    withAuth(checkQuota(QuotaCost.AUDIENCE_ANALYSIS, QuotaType.AUDIENCE_ANALYSIS)),
    async (request: FastifyRequest, reply) => {
      const { projectId, platform, source } = request.body as any
      const user = (request as any).user

      const validPlatforms = [KolPlatform.TIKTOK, KolPlatform.YOUTUBE, KolPlatform.INSTAGRAM]
      if (!projectId || !platform || !source || !validPlatforms.includes(platform)) {
        return reply.status(400).send(notFoundResponse('projectId,platform or source is invalid'))
      }

      const params: AudienceAnalysisTaskParams = {
        projectId,
        platform,
        source,
      }

      switch (platform) {
        case KolPlatform.TIKTOK:
          const userDetail = await TiktokApi.getInstance().getUserDetail({
            unique_id: params.source.toLowerCase(),
          })
          if (userDetail?.user?.uniqueId) {
            params.source = userDetail.user.uniqueId
          } else {
            return reply
              .status(400)
              .send(
                errorResponse(
                  StatusCodes.BAD_REQUEST,
                  `User ${source} cannot found. Please try again.`,
                  `User ${source} cannot found. Please try again.`,
                ),
              )
          }
          break
        case KolPlatform.INSTAGRAM:
          let insUser
          try {
            insUser = await InstagramRapidApiV3.getInstance().getUserWithoutAbout({
              username: source,
            })
            if (!insUser?.id) {
              return reply
                .status(400)
                .send(
                  errorResponse(
                    StatusCodes.BAD_REQUEST,
                    `User ${source} cannot found. Please try again.`,
                    `User ${source} cannot found. Please try again.`,
                  ),
                )
            }
          } catch (err) {
            console.error('create task failed:', err)
          }
          break
        default:
          break
      }

      try {
        const task = await TaskService.getInstance().createTask(
          projectId,
          params,
          user.id,
          TaskReason.AUDIENCE_ANALYSIS,
          TaskType.AUDIENCE_ANALYSIS,
        )
        return reply.status(200).send(successResponse(task, 'success'))
      } catch (error) {
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  // 目前先检查,不扣除配额
  // todo 后续每个请求都会扣除配额，依据createUserIds
  fastify.get(
    '/audienceAnalysis',
    withAuth(checkQuota(QuotaCost.AUDIENCE_ANALYSIS)),
    async (request: FastifyRequest, reply) => {
      const { platform, source } = request.query as any
      const user = (request as any).user

      let validSource = source
      const validPlatforms = [KolPlatform.TIKTOK, KolPlatform.YOUTUBE, KolPlatform.INSTAGRAM]

      if (!platform || !validPlatforms.includes(platform)) {
        return reply.status(400).send(notFoundResponse('Invalid platform'))
      }

      if (platform === KolPlatform.TIKTOK) {
        const userDetail = await TiktokApi.getInstance().getUserDetail({
          unique_id: source.toLowerCase(),
        })

        if (userDetail?.user?.uniqueId) {
          validSource = userDetail.user.uniqueId
        }
      }

      if (!validSource) {
        return reply.status(400).send(notFoundResponse('Invalid source'))
      }

      try {
        const kolInfo = await prisma.kolInfo.findUnique({
          where: {
            platform_platformAccount: {
              platform,
              platformAccount: validSource,
            },
          },
          select: {
            audienceAnalysis: true,
          },
        })

        if (!kolInfo || !kolInfo.audienceAnalysis) {
          return reply.status(200).send(
            successResponse(
              {
                userPortraitResult: null,
                regionAnalysisResult: null,
                fakeRadarData: null,
                kolId: null,
                updatedAt: null,
                needCreateTask: true,
              },
              'kolInfo not found,need create task to analysis',
            ),
          )
        }
        const { userPortraitResult, regionAnalysisResult, fakeRadarData, updatedAt, kolId } =
          kolInfo.audienceAnalysis as any

        // limit 30 days
        const thirtyDaysAgo = new Date()
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
        const updateTime = new Date(updatedAt)

        if (updateTime < thirtyDaysAgo) {
          return reply.status(200).send(
            successResponse(
              {
                userPortraitResult: null,
                regionAnalysisResult: null,
                fakeRadarData: null,
                kolId: null,
                updatedAt: null,
                needCreateTask: true,
              },
              'audienceAnalysis is expired,need create task to analysis',
            ),
          )
        }

        if (!userPortraitResult || !regionAnalysisResult) {
          return reply.status(200).send(
            successResponse(
              {
                userPortraitResult: null,
                regionAnalysisResult: null,
                fakeRadarData: null,
                kolId: null,
                updatedAt: null,
                needCreateTask: true,
              },
              'audienceAnalysis is empty,need create task to analysis',
            ),
          )
        }

        return reply.status(200).send(
          successResponse({
            userPortraitResult,
            regionAnalysisResult,
            fakeRadarData,
            kolId,
            updatedAt,
            needCreateTask: false,
          }),
        )
      } catch (error) {
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  // 导出受众分析excel
  fastify.get('/audienceAnalysis/export', withAuth(), async (request: FastifyRequest, reply) => {
    const { projectId, platform, source } = request.query as any
    const user = (request as any).user
    assert(
      Object.values(KolPlatform).includes(platform as KolPlatform),
      new Error('platform is invalid!'),
    )
    assert(source, new Error('source is required!'))
    try {
      // projectId 作用是找到启动过任务的那个task。只限定创建过任务人能够导出（暂废除）
      // 找到最近的一个source的受众分析
      const task = await prisma.similarChannelTask.findFirst({
        where: {
          type: TaskType.AUDIENCE_ANALYSIS,
          params: {
            path: ['source'],
            equals: source,
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      })
      assert(task, new Error('task not found, export failed'))
      const audienceAnalysis = await prisma.audienceAnalysis.findMany({
        where: {
          taskId: task.id,
        },
        orderBy: [{ region: 'desc' }, { city: 'desc' }],
      })
      assert(
        audienceAnalysis && audienceAnalysis.length,
        new Error('No audience analysis data found'),
      )

      console.log(
        `[ExportAudienceAnalysis] Found ${audienceAnalysis.length} records for task ${task.id}`,
      )

      // 获取用户的时区设置
      const membership = await MembershipService.getInstance().getMembership(user.id)

      const buffer = await AudienceService.exportAudienceAnalysisExcel(audienceAnalysis, source)

      const dateStr = TimezoneService.formatToUserTimezone(
        new Date(),
        membership?.timezone || 'Asia/Shanghai',
      )
        .slice(0, 19)
        .replace(/\s+/g, '_')
        .replace(/[T:]/g, '-')

      const fileName = `@${source}_${dateStr}.xlsx`
      const filePath = `excel/${fileName}`

      await uploadFileByBuffer(filePath, buffer)

      return reply.send(
        successResponse(
          {
            url: `${HOST ?? ''}/files/excel/${fileName}`,
          },
          'success',
        ),
      )
    } catch (error) {
      console.error('[ExportAudienceAnalysis] Error:', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 获取hashTag的结果
  fastify.get('/web/hashTag', withAuth(), async (request: FastifyRequest, reply) => {
    const { projectId, platform, page = 1, pageSize = 100 } = request.query as any
    assert(projectId, 'projectId is required!')
    assert(Object.values(KolPlatform).includes(platform as KolPlatform), 'platform is invalid!')
    assert(!isNaN(page) && !isNaN(pageSize), 'page and pageSize must be numbers')

    try {
      const result = await getProjectCandidatesWithPostsByTaskType(
        projectId,
        TaskType.HASH_TAG_BREAK,
        platform,
        {
          page,
          pageSize,
        },
      )
      return reply.status(200).send(successResponse(result))
    } catch (error) {
      if (error instanceof AssertionError) {
        return reply
          .status(400)
          .send(errorResponse(StatusCodes.BAD_REQUEST, 'VALIDATION_ERROR', error.message))
      }
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 获取searchInput的结果
  fastify.get('/web/searchInput', withAuth(), async (request: FastifyRequest, reply) => {
    const { projectId, platform, page = 1, pageSize = 100 } = request.query as any
    if (!projectId || !platform) {
      return reply.status(400).send(serverErrorResponse('projectId and platform are required'))
    }
    if (isNaN(page) || isNaN(pageSize)) {
      return reply.status(400).send(serverErrorResponse('page and pageSize must be numbers'))
    }
    try {
      switch (platform) {
        case KolPlatform.TIKTOK:
          const result = await getProjectCandidatesWithPostsByTaskType(
            projectId,
            TaskType.SEARCH_INPUT_BREAK,
            platform,
            {
              page,
              pageSize,
            },
          )
          return reply.status(200).send(successResponse(result))
        default:
          return reply.status(400).send(serverErrorResponse('platform is invalid'))
      }
    } catch (error) {
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.get('/web/followersSimilar', withAuth(), async (request: FastifyRequest, reply) => {
    const { projectId, platform } = request.query as any
    if (!projectId || !platform) {
      return reply.status(400).send(serverErrorResponse('projectId and platform are required'))
    }
    try {
      switch (platform) {
        case KolPlatform.TIKTOK:
          const result = await getProjectCandidatesWithPostsByTaskType(
            projectId,
            TaskType.FOLLOWERS_SIMILAR,
            platform,
          )
          return reply.status(200).send(successResponse(result))
        default:
          return reply.status(400).send(serverErrorResponse('platform is invalid'))
      }
    } catch (error) {
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.get('/web/followingList', withAuth(), async (request: FastifyRequest, reply) => {
    const { projectId, platform, page, pageSize } = request.query as any
    assert(projectId, 'projectId is required!')
    assert(Object.values(KolPlatform).includes(platform as KolPlatform), 'platform is invalid!')
    assert(!isNaN(page) && !isNaN(pageSize), 'page and pageSize must be numbers')
    try {
      const result = await getProjectCandidatesWithPostsByTaskType(
        projectId,
        TaskType.FOLLOWING_LIST,
        platform,
        {
          page,
          pageSize,
        },
      )
      return reply.status(200).send(successResponse(result))
    } catch (error) {
      if (error instanceof AssertionError) {
        return reply
          .status(400)
          .send(errorResponse(StatusCodes.BAD_REQUEST, 'VALIDATION_ERROR', error.message))
      }
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  // 获取hashTag的结果
  fastify.get('/web/bgm', withAuth(), async (request: FastifyRequest, reply) => {
    const { projectId, platform, page = 1, pageSize = 100 } = request.query as any
    assert(projectId, 'projectId is required!')
    assert(Object.values(KolPlatform).includes(platform as KolPlatform), 'platform is invalid!')
    assert(!isNaN(page) && !isNaN(pageSize), 'page and pageSize must be numbers')

    try {
      const result = await getProjectCandidatesWithPostsByTaskType(
        projectId,
        TaskType.BGM_BREAK,
        platform,
        {
          page,
          pageSize,
        },
      )
      return reply.status(200).send(successResponse(result))
    } catch (error) {
      if (error instanceof AssertionError) {
        return reply
          .status(400)
          .send(errorResponse(StatusCodes.BAD_REQUEST, 'VALIDATION_ERROR', error.message))
      }
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.get('/web/webList', withAuth(), async (request: FastifyRequest, reply) => {
    const { projectId, platform, page = 1, pageSize = 100 } = request.query as any
    assert(projectId, 'projectId is required!')
    assert(Object.values(KolPlatform).includes(platform as KolPlatform), 'platform is invalid!')
    assert(!isNaN(page) && !isNaN(pageSize), 'page and pageSize must be numbers')

    try {
      const result = await getProjectCandidatesWithPostsByTaskType(
        projectId,
        TaskType.WEB_LIST,
        platform,
        {
          page,
          pageSize,
        },
      )
      return reply.status(200).send(successResponse(result))
    } catch (error) {
      if (error instanceof AssertionError) {
        return reply
          .status(400)
          .send(errorResponse(StatusCodes.BAD_REQUEST, 'VALIDATION_ERROR', error.message))
      }
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.get('/web/taggedBreak', withAuth(), async (request: FastifyRequest, reply) => {
    const { projectId, platform, page = 1, pageSize = 100 } = request.query as any
    assert(projectId, 'projectId is required!')
    assert(Object.values(KolPlatform).includes(platform as KolPlatform), 'platform is invalid!')
    assert(!isNaN(page) && !isNaN(pageSize), 'page and pageSize must be numbers')

    try {
      const result = await getProjectCandidatesWithPostsByTaskType(
        projectId,
        TaskType.TAGGED_BREAK,
        platform,
        {
          page,
          pageSize,
        },
      )
      return reply.status(200).send(successResponse(result))
    } catch (error) {
      if (error instanceof AssertionError) {
        return reply
          .status(400)
          .send(errorResponse(StatusCodes.BAD_REQUEST, 'VALIDATION_ERROR', error.message))
      }
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.get('/web/similar', withAuth(), async (request: FastifyRequest, reply) => {
    const { projectId } = request.query as any
    const data = await similarSearchForAllPlatforms(projectId)
    return reply.status(200).send(successResponse(data))
  })

  fastify.get<{ Querystring: WebExportParams }>(
    '/web/export',
    {
      schema: {
        tags: ['search'],
        summary: '导出搜索结果',
        description: '导出搜索结果到Excel',
        querystring: WebExportParamsSchema,
      },
      // ...withAuth(),
    },
    async (request, reply) => {
      const { taskId } = request.query as WebExportParams
      const file = await exportTaskResult(taskId)
      reply.header(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      )
      reply.header(
        'Content-Disposition',
        `attachment; filename=export-${taskId}-${new Date().toISOString().substring(0, 10)}.xlsx`,
      )
      return reply.send(file)
    },
  )

  /**
   * 创建长时间爬取任务
   */
  fastify.post<{ Body: LongCrawlerRequest }>(
    '/longCrawler',
    {
      schema: {
        tags: ['search'],
        summary: '创建长时间爬取任务',
        description: '创建一个长时间运行的爬取任务，用于持续爬取符合条件的用户',
        body: LongCrawlerRequestSchema,
        response: FullResponseSchema(LongCrawlerTaskResponseSchema),
      },
      ...withAuth(dynamicCheckQuota(DynamicQuotaService.calculateLongCrawlerQuota)),
    },
    async (request: FastifyRequest<{ Body: LongCrawlerRequest }>, reply) => {
      const { platform, projectId, filters, seedUsers, numberOfRuns } = request.body
      const user = (request as any).user

      const project = await prisma.project.findUnique({
        where: {
          id: projectId,
          deletedAt: null,
        },
      })

      if (!project) {
        throwError(StatusCodes.BAD_REQUEST, 'Project not found')
      }

      // 并发验证所有种子用户
      let validatedSeedUsers: any[] = []

      switch (platform) {
        case KolPlatform.INSTAGRAM: {
          try {
            console.info(`[longCrawler] Starting validation of ${seedUsers.length} Instagram seed users`)
            const userValidationResults = await Promise.allSettled(
              seedUsers.map(async (username) => {
                const user = await InstagramRapidApiV3.getInstance().getUser({ username })
                if (!user?.id) {
                  throw new Error(`Instagram user ${username} does not exist`)
                }
                return { username, user }
              })
            )

            const failedUsers: string[] = []
            validatedSeedUsers = userValidationResults
              .map((result, index) => {
                if (result.status === 'fulfilled') {
                  return result.value
                } else {
                  failedUsers.push(`${seedUsers[index]}: ${result.reason.message}`)
                  return null
                }
              })
              .filter(Boolean)

            if (failedUsers.length > 0) {
              throwError(
                StatusCodes.BAD_REQUEST,
                `The following Instagram seed users failed validation: ${failedUsers.join(', ')}`
              )
            }

            console.info(`[longCrawler] Instagram seed users validation successful: ${validatedSeedUsers.length}/${seedUsers.length}`)
          } catch (err) {
            console.error('Instagram seed users validation failed:', err)
            throwError(StatusCodes.BAD_REQUEST, (err as Error)?.message || 'Instagram seed users validation failed')
          }
          break
        }
        case KolPlatform.YOUTUBE: {
          try {
            console.info(`[longCrawler] Starting validation of ${seedUsers.length} YouTube seed users`)
            const channelValidationResults = await Promise.allSettled(
              seedUsers.map(async (username) => {
                const channel = await YoutubeApi.getInstance().getChannelWithVideos(username)
                if (!channel?.channelId) {
                  throw new Error(`YouTube channel ${username} does not exist`)
                }
                if (!channel.videos || channel.videos.length === 0) {
                  throw new Error(`YouTube channel ${username} has no video data`)
                }
                return { username, channel }
              })
            )

            const failedChannels: string[] = []
            validatedSeedUsers = channelValidationResults
              .map((result, index) => {
                if (result.status === 'fulfilled') {
                  return result.value
                } else {
                  failedChannels.push(`${seedUsers[index]}: ${result.reason.message}`)
                  return null
                }
              })
              .filter(Boolean)

            if (failedChannels.length > 0) {
              throwError(
                StatusCodes.BAD_REQUEST,
                `The following YouTube seed users failed validation: ${failedChannels.join(', ')}`
              )
            }

            console.info(`[longCrawler] YouTube seed users validation successful: ${validatedSeedUsers.length}/${seedUsers.length}`)
          } catch (err) {
            console.error('YouTube seed users validation failed:', err)
            throwError(StatusCodes.BAD_REQUEST, (err as Error)?.message || 'YouTube seed users validation failed')
          }
          break
        }
        default:
          throwError(StatusCodes.BAD_REQUEST, 'Only Instagram and YouTube platforms are supported')
      }

      // 初始化任务参数和优先级队列
      let taskParams: any
      let initialProgress: any

      switch (platform) {
        case KolPlatform.INSTAGRAM: {
          taskParams = {
            projectId,
            platform,
            filters: {
              ...filters,
              regions: filters.regions?.map((region) => region.toUpperCase()) || [],
            },
            seedUsers,
            maxQuotaCost: numberOfRuns * 10,
          } as InsLongCrawlerTaskParams

          // 初始化Instagram优先级队列
          const priorityQueue = PriorityQueueFactory.createQueue(
            KolPlatform.INSTAGRAM,
          ) as InstagramPriorityQueue

          // 将验证通过的种子用户添加到队列
          validatedSeedUsers.forEach(({ username }) => {
            const seedUser: InstagramPriorityUser = {
              username,
              attitude: ProjectKolAttitude.NORATE,
              priority: calculateUserPriority(ProjectKolAttitude.NORATE, 'seed', 'perfect'),
              addedAt: Date.now(),
              source: 'seed',
              userType: 'perfect',
            }
            priorityQueue.enqueue(seedUser)
          })

          console.info(`[longCrawler] Instagram queue initialization completed with ${priorityQueue.size()} seed users`)

          initialProgress = {
            processedUsers: [],
            pendingUsersQueue: priorityQueue.toJSON(),
            allPerfectFitUserNames: [],
            allContentOkUserNames: [],
            lastProcessedAt: new Date().toISOString(),
            currentBatch: 0,
            batchLogs: [],
            hasConsumedQuotaCount: 0,
            allProcessedUsers: [],
          } as InsLongCrawlerProgress
          break
        }
        case KolPlatform.YOUTUBE: {
          taskParams = {
            projectId,
            platform,
            filters: {
              ...filters,
              regions: filters.regions?.map((region) => region.toUpperCase()) || [],
            },
            seedUsers,
            maxQuotaCost: numberOfRuns * 10,
          } as YtbLongCrawlerTaskParams

          // 初始化YouTube优先级队列
          const priorityQueue = PriorityQueueFactory.createQueue(
            KolPlatform.YOUTUBE,
          ) as YoutubePriorityQueue

          // 将验证通过的种子用户添加到队列（包含videoIds）
          const validChannelsWithVideos: any[] = []
          validatedSeedUsers.forEach(({ username, channel }) => {
            // 获取前6个视频的videoIds
            const videoIds = channel.videos
              ?.slice(0, 6)
              .map((video: any) => video.videoId)
              .filter(Boolean) || []

            if (videoIds.length > 0) {
              const seedUser: YoutubePriorityUser = {
                username: channel.channelId, // 使用channelId作为username
                attitude: ProjectKolAttitude.NORATE,
                priority: calculateUserPriority(ProjectKolAttitude.NORATE, 'seed', 'perfect'),
                addedAt: Date.now(),
                source: 'seed',
                userType: 'perfect',
                videoIds,
              }
              priorityQueue.enqueue(seedUser)
              validChannelsWithVideos.push({ username, channel, videoIds })
              console.info(`[longCrawler] Added YouTube seed user ${username} (${channel.channelId}) to queue with ${videoIds.length} videos`)
            } else {
              console.warn(`[longCrawler] YouTube seed user ${username} has no valid video IDs, skipping`)
            }
          })

          // Check if queue is empty
          if (priorityQueue.isEmpty()) {
            throwError(
              StatusCodes.BAD_REQUEST,
              'All YouTube seed users have no valid video data, cannot create task'
            )
          }

          console.info(`[longCrawler] YouTube queue initialization completed with ${priorityQueue.size()} valid seed users`)

          initialProgress = {
            processedUsers: [],
            pendingUsersQueue: priorityQueue.toJSON(),
            allPerfectFitUserNames: [],
            allContentOkUserNames: [],
            lastProcessedAt: new Date().toISOString(),
            currentBatch: 0,
            batchLogs: [],
            hasConsumedQuotaCount: 0,
            allProcessedUsers: [],
          } as YtbLongCrawlerProgress
          break
        }
        default:
          throwError(StatusCodes.BAD_REQUEST, `Unsupported platform: ${platform}`)
      }

      const task = await TaskService.getInstance().createLongCrawlerTask(
        projectId,
        taskParams,
        user.id,
        TaskReason.SEARCH,
        TaskType.LONG_CRAWLER,
        { ...initialProgress },
      )

      return reply.status(200).send(successResponse(task, 'Task created successfully'))
    },
  )

  fastify.post<{
    Params: { taskId: string }
    Body: { kolId: string; attitude: ProjectKolAttitude }
  }>(
    '/longCrawler/:taskId/rate',
    {
      schema: {
        tags: ['search'],
        summary: '长时间爬取任务用户评分',
        description: '对长时间爬取任务中的用户进行评分，影响处理优先级',
        params: LongCrawlerRateParamsSchema,
        body: LongCrawlerRateBodySchema,
        response: FullResponseSchema(LongCrawlerRateDataSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { taskId } = request.params
      const { kolId, attitude } = request.body
      const userId = (request as any).user.id

      const task = await prisma.similarChannelTask.findUniqueOrThrow({
        where: { id: taskId },
      })

      if (task.createdBy !== userId) {
        throwError(StatusCodes.FORBIDDEN, 'Access denied to this task')
      }

      const kolInfo = await prisma.kolInfo.findUniqueOrThrow({
        where: { id: kolId },
      })

      if (!kolInfo.platformAccount) {
        throwError(StatusCodes.BAD_REQUEST, 'KOL platform account information is missing')
      }

      const progress = task.meta  as unknown as InsLongCrawlerProgress | YtbLongCrawlerProgress

      // 验证用户是否在 allPerfectFitUserNames 或 allContentOkUserNames 中
      const isInPerfectFit =
        progress.allPerfectFitUserNames?.includes(kolInfo.platformAccount) || false
      const isInContentOk =
        progress.allContentOkUserNames?.includes(kolInfo.platformAccount) || false

      if (!isInPerfectFit && !isInContentOk) {
        throwError(StatusCodes.BAD_REQUEST, 'Rate failed,data wrong')
      }

      // 检查用户是否已在 processedUsers 中
      const isAlreadyProcessed = progress.processedUsers?.includes(kolInfo.platformAccount) || false

      const existingProjectKol = await prisma.projectKol.findFirst({
        where: {
          projectId: task.projectId,
          kolId: kolId,
        },
      })

      let projectKol
      if (existingProjectKol) {
        projectKol = await prisma.projectKol.update({
          where: { id: existingProjectKol.id },
          data: {
            attitude,
            rateBy: userId,
          },
        })
      } else {
        projectKol = await prisma.projectKol.create({
          data: {
            projectId: task.projectId,
            kolId: kolId,
            similarTaskId: taskId,
            attitude,
            rateBy: userId,
          },
        })
      }
      // 只有当用户未被处理时，才更新队列
      if (!isAlreadyProcessed) {
        switch (kolInfo.platform) {
          case KolPlatform.INSTAGRAM: {
            if (!progress.pendingUsersQueue) {
              throwError(StatusCodes.BAD_REQUEST, 'Task queue not found, cannot update priority')
            }
            const queue = PriorityQueueFactory.fromJSON(
              progress.pendingUsersQueue,
              KolPlatform.INSTAGRAM,
            ) as InstagramPriorityQueue
            if (queue.contains(kolInfo.platformAccount)) {
              // 获取现有用户信息
              const existingUser = queue.getUser(kolInfo.platformAccount)
              if (!existingUser) {
                throwError(StatusCodes.SERVER_ERROR, 'User exists in queue but cannot be retrieved')
              }

              const updatedUser: InstagramPriorityUser = {
                username: kolInfo.platformAccount,
                attitude,
                priority: calculateUserPriority(attitude, 'user_manual', existingUser.userType),
                addedAt: Date.now(), // 更新为当前时间
                source: 'user_manual', // 改为手动来源
                userType: existingUser.userType, // 从现有用户获取
                metadata: existingUser.metadata, // 保留元数据
              }
              queue.updateUser(updatedUser)

              // 更新任务进度
              const updatedProgress = {
                ...progress,
                pendingUsersQueue: queue.toJSON(),
              }

              await prisma.similarChannelTask.update({
                where: { id: taskId },
                data: { meta: updatedProgress },
              })
            }
            break
          }
          case KolPlatform.YOUTUBE: {
            if (!progress.pendingUsersQueue) {
              throwError(StatusCodes.BAD_REQUEST, 'Task queue not found, cannot update priority')
            }
            const queue = PriorityQueueFactory.fromJSON(
              progress.pendingUsersQueue,
              KolPlatform.YOUTUBE,
            ) as YoutubePriorityQueue
            if (queue.contains(kolInfo.platformAccount)) {
  
              const existingUser = queue.getUser(kolInfo.platformAccount)
              if (!existingUser) {
                throwError(StatusCodes.SERVER_ERROR, 'User exists in queue but cannot be retrieved')
              }

              const updatedUser: YoutubePriorityUser = {
                username: kolInfo.platformAccount,
                attitude,
                priority: calculateUserPriority(attitude, 'user_manual', existingUser.userType),
                addedAt: Date.now(), 
                source: 'user_manual', 
                userType: existingUser.userType, // 从现有用户获取
                videoIds: existingUser.videoIds, // 保留videoIds
                metadata: existingUser.metadata, // 保留元数据
              }
              queue.updateUser(updatedUser)

              // 更新任务进度
              const updatedProgress = {
                ...progress,
                pendingUsersQueue: queue.toJSON(),
              }

              await prisma.similarChannelTask.update({
                where: { id: taskId },
                data: { meta: updatedProgress },
              })
            }
            break
          }
          default:
            break
        }
      }

      return reply.send(
        successResponse({
          ...projectKol,
          isAlreadyProcessed,
        }),
      )
    },
  )

  /**
   * 获取任务状态
   */
  fastify.get<{ Params: TaskStatusQueryParams }>(
    '/longCrawler/:taskId',
    {
      schema: {
        tags: ['search'],
        summary: '获取任务状态',
        description: '获取长时间爬取任务的当前状态和进度',
        params: TaskStatusQueryParamsSchema,
        response: FullResponseSchema(TaskStatusResponseSchema),
      },
      ...withAuth(),
    },
    async (request: FastifyRequest<{ Params: TaskStatusQueryParams }>, reply) => {
      const { taskId } = request.params
      const user = (request as any).user

      const task = await prisma.similarChannelTask.findUnique({
        where: {
          id: taskId,
        },
      })
      if (!task) {
        throwError(StatusCodes.NOT_FOUND, 'task not found')
      }

      if (!(task?.createdBy === user.id)) {
        throwError(StatusCodes.FORBIDDEN, 'no permission to view this task')
      }

      const taskParams = task?.params as unknown as TaskTypeToParamsMap['LONG_CRAWLER']
      const platform = taskParams?.platform

      let taskProgeress:any
      let latestUsersData: any

      switch (platform) {
        case KolPlatform.INSTAGRAM: {

          const { allProcessedUsers, ...progress } =  task?.meta as unknown as InsLongCrawlerProgress || {}
          taskProgeress=progress

          // 获取最新5个perfect-fit和content-ok用户的数据
          latestUsersData =
            await InstagramService.getInstance().getLatestPerfectFitAndContentOkUsers(taskId, user.id)
          break
        }
        case KolPlatform.YOUTUBE: {
          const { allProcessedUsers, ...progress } = task?.meta as unknown as YtbLongCrawlerProgress || {}
           taskProgeress=progress
          // 获取最新5个perfect-fit和content-ok用户的数据
          latestUsersData =
            await YoutubeService.getInstance().getLatestPerfectFitAndContentOkUsers(taskId, user.id)
          break
        }
        default:
          throwError(StatusCodes.BAD_REQUEST, `Unsupported platform: ${platform}`)
      }

      const responseData = {
        taskId: task?.id,
        type: task?.type,
        reason: task?.reason,
        status: task?.status,
        meta: taskProgeress,
        params: task?.params,
        latestPerfectFitUsers: latestUsersData.perfectFitUsers, // 最新5个perfect-fit用户
        latestContentOkUsers: latestUsersData.contentOkUsers, // 最新5个content-ok用户
        createdAt: task?.createdAt,
        updatedAt: task?.updatedAt,
      }

      return reply.status(200).send(successResponse(responseData))
    },
  )

  // Perfect Fit,Others – Content OK
  fastify.post<{ Body: LongCrawlerExportRequest }>(
    '/longCrawler/export',
    {
      schema: {
        tags: ['search'],
        summary: '导出长时间爬取任务结果',
        description: '导出长时间爬取任务结果到Excel',
        body: LongCrawlerExportSchema,
        response: FullResponseSchema(LongCrawlerExportResponseSchema),
      },
      ...withAuth(),
    },
    async (request: FastifyRequest<{ Body: LongCrawlerExportRequest }>, reply) => {
      const { taskId, type } = request.body
      const user = (request as any).user

      if (!['contentOk', 'perfectFit'].includes(type)) {
        throwError(StatusCodes.BAD_REQUEST, 'type should be contentOk or perfectFit')
      }

      const task = await prisma.similarChannelTask.findUniqueOrThrow({
        where: {
          id: taskId,
        },
      })

      if (!task.candidate) {
        throwError(StatusCodes.BAD_REQUEST, 'No candidate data found in task')
      }

      const candidateData = task.candidate as unknown as {
        timestamp: string
        contentOkUsers: QueueUser[]
        perfectFitUsers: QueueUser[]
      }
      const users =
        type === 'contentOk'
          ? candidateData.contentOkUsers || []
          : candidateData.perfectFitUsers || []
      const usernames = users.map((user) => user.username)

      if (usernames.length === 0) {
        throwError(StatusCodes.BAD_REQUEST, `No ${type} users found in task`)
      }

      const params = task.params as unknown as TaskTypeToParamsMap['LONG_CRAWLER']
      const platform = params.platform

      if (!['INSTAGRAM', 'YOUTUBE'].includes(platform)) {
        throwError(StatusCodes.BAD_REQUEST, 'Only INSTAGRAM and YOUTUBE platforms are supported')
      }

      let exportData: any[]

      switch (platform) {
        case 'INSTAGRAM': {
          const [kolInfos, instagramUsers] = await Promise.all([
            prisma.kolInfo.findMany({
              where: {
                platform: KolPlatform.INSTAGRAM,
                platformAccount: {
                  in: usernames,
                },
              },
            }),
            prisma.instagramUserInfo.findMany({
              where: {
                username: {
                  in: usernames,
                },
              },
            }),
          ])

          const kolInfoMap = new Map(kolInfos.map((kol) => [kol.platformAccount, kol]))
          const instagramUserMap = new Map(instagramUsers.map((user) => [user.username, user]))

          exportData = usernames.map((username: string) => {
            const kolInfo = kolInfoMap.get(username)
            const instagramUser = instagramUserMap.get(username)

            return {
              kolTitle: instagramUser?.fullName || kolInfo?.title || username,
              platformAccount: username,
              country: instagramUser?.region || '',
              platform: KolPlatform.INSTAGRAM,
              numericSubscriberCount: String(instagramUser?.followerCount || 0),
              videosAverageViewCount: String(instagramUser?.averageLikeCount || 0),
              kolEmail: kolInfo?.email || '',
              url: `https://instagram.com/${username}`,
            }
          })
          break
        }
        case 'YOUTUBE': {
          const [kolInfos, youtubeChannels] = await Promise.all([
            prisma.kolInfo.findMany({
              where: {
                platform: KolPlatform.YOUTUBE,
                platformAccount: {
                  in: usernames,
                },
              },
            }),
            prisma.youTubeChannel.findMany({
              where: {
                channelId: {
                  in: usernames,
                },
              },
            }),
          ])

          const kolInfoMap = new Map(kolInfos.map((kol) => [kol.platformAccount, kol]))
          const youtubeChannelMap = new Map(youtubeChannels.map((channel) => [channel.channelId, channel]))

          exportData = usernames.map((channelId: string) => {
            const kolInfo = kolInfoMap.get(channelId)
            const youtubeChannel = youtubeChannelMap.get(channelId)

            return {
              kolTitle: youtubeChannel?.channelName || kolInfo?.title || channelId,
              platformAccount: youtubeChannel?.channelHandle || channelId,
              country: youtubeChannel?.country || '',
              platform: KolPlatform.YOUTUBE,
              numericSubscriberCount: String(youtubeChannel?.numericSubscriberCount || 0),
              videosAverageViewCount: String(youtubeChannel?.videosAverageViewCount || 0),
              kolEmail: kolInfo?.email || '',
              url: `https://youtube.com/channel/${channelId}`,
            }
          })
          break
        }
        default:
          exportData = []
      }

      // 准备Excel表格
      const sheets = [
        {
          name: type === 'contentOk' ? 'Others – Content OK' : 'Perfect Fit',
          data: exportData,
          columns: LongCrawlerExportColumns,
          styles: commonStyle,
        },
      ]

      // 生成并返回Excel文件
      try {
        const buffer = await exportToExcel(sheets)
        const dateStr = new Date()
          .toISOString()
          .slice(0, 19)
          .replace(/\s+/g, '_')
          .replace(/[T:]/g, '-')

        const fileName = `${user.email}_${sheets[0].name}_${dateStr}.xlsx`
        const filePath = `excel/${fileName}`
        await uploadFileByBuffer(filePath, buffer)

        return reply.status(200).send(successResponse(`${HOST ?? ''}/files/excel/${fileName}`))
      } catch (error) {
        console.error('[Export] Error during export process:', error)
        throw error
      }
    },
  )

  /**
   * 更新长时间爬取任务参数
   */
  fastify.put<{ Body: UpdateLongCrawlerParams }>(
    '/longCrawler/params',
    {
      schema: {
        tags: ['search'],
        summary: '更新长时间爬取任务参数',
        description: '更新长时间爬取任务的配额消耗和过滤条件',
        body: UpdateLongCrawlerParamsSchema,
        response: FullResponseSchema(UpdateLongCrawlerParamsResponseSchema),
      },
      ...withAuth(dynamicCheckQuota(DynamicQuotaService.calculateAddLongCrawlerQuota)),
    },
    async (request: FastifyRequest<{ Body: UpdateLongCrawlerParams }>, reply) => {
      const { taskId, params } = request.body
      const user = (request as any).user

      const task = await prisma.similarChannelTask.findUniqueOrThrow({
        where: { id: taskId },
      })

      if (task.createdBy !== user.id) {
        throwError(StatusCodes.FORBIDDEN, 'no permission to access this task')
      }

      const currentParams = task.params as unknown as TaskTypeToParamsMap['LONG_CRAWLER']

      // calculate new quota (only if addNumberOfRuns is provided)
      const addQuota = params.addNumberOfRuns ? params.addNumberOfRuns * 10 : 0
      const newMaxQuotaCost = (currentParams.maxQuotaCost || 0) + addQuota

      const updatedParams = {
        ...currentParams,
        ...(params.addNumberOfRuns && { maxQuotaCost: newMaxQuotaCost }),
        ...(params.kolDescription && {
          filters: {
            ...currentParams.filters,
            kolDescription: params.kolDescription,
          },
        }),
      }

      const updatedTask = await prisma.similarChannelTask.update({
        where: { id: taskId },
        data: { params: updatedParams },
      })

      // 只有当修改了配额时才需要恢复已完成的任务
      if (params.addNumberOfRuns && updatedTask.status === SimilarChannelTaskStatus.COMPLETED) {
        await TaskService.getInstance().resumeCompletedLongCrawlerTask(updatedTask)
      }

      return reply.status(200).send(
        successResponse(
          {
            taskId: updatedTask.id,
            params: updatedTask.params,
            ...(params.addNumberOfRuns && {
              addedQuota: addQuota,
              newMaxQuotaCost: newMaxQuotaCost,
            }),
            ...(params.kolDescription && {
              updatedKolDescription: params.kolDescription,
            }),
          },
          params.addNumberOfRuns && params.kolDescription
            ? '任务参数更新成功'
            : params.addNumberOfRuns
              ? '任务配额增加成功'
              : '任务参数更新成功',
        ),
      )
    },
  )

  /**
   * 获取长时间爬取任务的用户数据
   */
  fastify.get<{
    Params: LongCrawlerUsersParams
    Querystring: LongCrawlerUsersQuery
  }>(
    '/longCrawler/:taskId/users',
    {
      schema: {
        tags: ['search'],
        summary: '获取长时间爬取任务的用户数据',
        description: '分页获取长时间爬取任务中Perfect-Fit或Content-OK用户的完整信息',
        params: LongCrawlerUsersParamsSchema,
        querystring: LongCrawlerUsersQuerySchema,
        response: FullResponseSchema(LongCrawlerUsersResponseSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { taskId } = request.params
      const { page = 1, pageSize = 100, attitude, type } = request.query
      const user = (request as any).user

      // 获取任务信息以确定平台
      const task = await prisma.similarChannelTask.findUniqueOrThrow({
        where: { id: taskId },
      })

      if (task.createdBy !== user.id) {
        throwError(StatusCodes.FORBIDDEN, 'no permission to access this task')
      }

      const taskParams = task.params as unknown as TaskTypeToParamsMap['LONG_CRAWLER']
      const platform = taskParams?.platform

      let result: any

      switch (platform) {
        case KolPlatform.INSTAGRAM: {
          result = await InstagramService.getInstance().getLongCrawlerUsers(
            taskId,
            user.id,
            page,
            pageSize,
            type as 'perfectFit' | 'contentOk',
            attitude,
          )
          break
        }
        case KolPlatform.YOUTUBE: {
          result = await YoutubeService.getInstance().getLongCrawlerUsers(
            taskId,
            user.id,
            page,
            pageSize,
            type as 'perfectFit' | 'contentOk',
            attitude,
          )
          break
        }
        default:
          throwError(StatusCodes.BAD_REQUEST, `Unsupported platform: ${platform}`)
      }

      return reply.status(200).send(successResponse(result, 'Successfully retrieved user data'))
    },
  )
}

export default router
