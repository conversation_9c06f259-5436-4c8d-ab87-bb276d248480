import { handleUnknownError } from '@/common/errorHandler'
import { StatusCodes } from '@/common/errors/statusCodes'
import { errorResponse, serverErrorResponse, successResponse } from '@/common/response/response'
import { FullResponseSchema } from '@/config/swagger'
import { QuotaCost } from '@/enums/QuotaCost'
import InstagramRapidApiV3 from '@/lib/instagramRapidApi.v3'
import { withAuth } from '@/middlewares/auth'
import { dynamicCheckQuota, handleQuotaWithResponse, responseBasedQuota } from '@/middlewares/quota'
import { DynamicQuotaService } from '@/services/dynamicQuota.service'
import { InsInfoService } from '@/services/insInfo.service'
import { getProjectCandidatesWithPostsByTaskType } from '@/services/project'
import TaskService from '@/services/task.ts'
import { InstagramSortType } from '@/types/instagram'
import {
  InsFollowingListTaskParams,
  InsHashTagBreakTaskParams,
  InsTaggedBreakTaskParams,
  InsWebListTaskParams,
} from '@/types/task.ts'
import {
  KolPlatform,
  QuotaType,
  SimilarChannelTaskStatus,
  TaskReason,
  TaskType,
  prisma,
} from '@repo/database'
import assert, { AssertionError } from 'assert'
import { FastifyPluginAsync, FastifyRequest } from 'fastify'
import { PaginateReq, PaginateReqSchema } from '../schemas/common'
import {
  AudienceFakeExportReq,
  AudienceFakeExportReqSchema,
  AudienceFakeExportResSchema,
  AudienceFakeReq,
  AudienceFakeReqSchema,
  AudienceFakeResSchema,
  InsFollowingListReq,
  InsFollowingListReqSchema,
  InsHashTagBreakReq,
  InsHashTagBreakReqSchema,
  InsTaggedBreakReq,
  InsTaggedBreakReqSchema,
  InsWebListReq,
  InsWebListReqSchema,
} from '../schemas/ins'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  // 创建 ins 的 hashTag 任务
  fastify.post<{ Body: InsHashTagBreakReq }>(
    '/hashTag',
    {
      schema: {
        summary: '创建 Hashtag 任务',
        description: '创建 Instagram 标签爆破任务',
        tags: ['Instagram'],
        security: [{ bearerAuth: [] }],
        body: InsHashTagBreakReqSchema,
        // response: FullResponseSchema(InsHashTagBreakResSchema),
      },
      ...withAuth(
        dynamicCheckQuota(
          DynamicQuotaService.calculateHashTagSearchQuota,
          QuotaType.INS_HASH_TAG_BREAK,
        ),
      ),
    },
    async (req, reply) => {
      try {
        const { projectId, reason, maxVideoCount = 100, sortType } = req.body
        let { tag } = req.body
        tag = tag.replace('#', '')
        tag = tag.trim()
        tag = tag.replace(/ /g, '')
        tag = tag.toLowerCase()
        let currentVideoCount = 0
        const user = (req as any).user

        assert(projectId, 'projectId is required!')
        assert(tag, 'tag is required and cannot be empty!')

        const project = await prisma.project.findUnique({
          where: { id: projectId, deletedAt: null },
        })
        assert(project, 'Project not found or has been deleted!')

        const hashTagInfo = await InstagramRapidApiV3.getInstance().getHashTagVideos(tag)
        assert(
          hashTagInfo && hashTagInfo.count && hashTagInfo.additional_data !== null,
          `Tag '${tag}' not found!`,
        )

        let paginationToken: string = ''
        if (reason !== TaskReason.SEARCH) {
          const projectCandidate = await prisma.projectCandidate.findUnique({
            where: {
              projectId_type: {
                projectId,
                type: TaskType.HASH_TAG_BREAK,
              },
            },
          })
          const meta = (projectCandidate?.meta as any) || {}
          paginationToken = meta.paginationToken || ''
          currentVideoCount = meta?.progress?.videoCount || 0
        }

        const params: InsHashTagBreakTaskParams = {
          projectId,
          platform: KolPlatform.INSTAGRAM,
          tag,
          sortType: (sortType as InstagramSortType) || InstagramSortType.TOP,
          paginationToken,
          currentVideoCount,
          maxVideoCount,
          total: hashTagInfo.count,
        }
        const task = await TaskService.getInstance().createTask(
          projectId,
          params,
          user.id,
          reason || TaskReason.SEARCH,
          TaskType.HASH_TAG_BREAK,
        )
        return reply.status(200).send(successResponse(task))
      } catch (error) {
        if (error instanceof AssertionError) {
          return reply
            .status(400)
            .send(errorResponse(StatusCodes.BAD_REQUEST, 'VALIDATION_ERROR', error.message))
        }
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  // 获取 ins 的 hashTag 结果
  fastify.get(
    '/hashTag',
    {
      schema: {
        summary: '获取 Hashtag 结果',
        description: '获取 Instagram 标签爆破结果',
        tags: ['Instagram'],
        security: [{ bearerAuth: [] }],
        querystring: PaginateReqSchema,
        // response: FullResponseSchema(InsHashTagListResSchema),
      },
      ...withAuth(),
    },
    async (request: FastifyRequest, reply) => {
      const { projectId, page = 1, pageSize = 100 } = request.query as PaginateReq
      if (!projectId) {
        return reply.status(400).send(serverErrorResponse('projectId is required'))
      }
      if (isNaN(page) || isNaN(pageSize)) {
        return reply.status(400).send(serverErrorResponse('page and pageSize must be numbers'))
      }
      try {
        const result = await getProjectCandidatesWithPostsByTaskType(
          projectId,
          TaskType.HASH_TAG_BREAK,
          KolPlatform.INSTAGRAM,
          {
            page,
            pageSize,
          },
        )
        return reply.status(200).send(successResponse(result))
      } catch (error) {
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  // 创建 instagram 的 following 任务
  fastify.post<{ Body: InsFollowingListReq }>(
    '/followingList',
    {
      schema: {
        description: '创建 Instagram 关注列表任务',
        tags: ['Instagram'],
        security: [{ bearerAuth: [] }],
        body: InsFollowingListReqSchema,
        // response: FullResponseSchema(InsHashTagBreakResSchema),
      },
      ...withAuth(
        dynamicCheckQuota(
          DynamicQuotaService.calculateFollowingBreakQuota,
          QuotaType.INS_FOLLOWING_LIST,
        ),
      ),
    },
    async (req: FastifyRequest<{ Body: InsFollowingListReq }>, reply) => {
      const {
        projectId,
        username,
        reason,
        currentCount = 0,
        maxCount = 100,
      } = req.body as InsFollowingListReq
      const user = (req as any).user

      assert(projectId, 'projectId is required!')
      assert(username && username.trim() !== '', 'username is required and cannot be empty!')

      try {
        let paginationToken = ''
        let total = 0
        if (reason !== TaskReason.SEARCH) {
          const projectCandidate = await prisma.projectCandidate.findUnique({
            where: {
              projectId_type: {
                projectId,
                type: TaskType.FOLLOWING_LIST,
              },
            },
          })
          const meta = (projectCandidate?.meta as any) || {}
          paginationToken = meta.paginationToken || ''
          total = meta?.progress?.total || 0
        } else {
          // 首先判断用户是否存在
          const userDetail = await InstagramRapidApiV3.getInstance().getUserWithoutAbout({
            username,
          })
          assert(userDetail, 'user not found')
          // 获取用户关注列表是否开放
          const followingResponse = await InstagramRapidApiV3.getInstance().getFollowings({
            username,
          })
          assert(followingResponse, 'user not open following list')
          total = userDetail.followingCount || 0
        }

        const taskParams: InsFollowingListTaskParams = {
          projectId,
          platform: KolPlatform.INSTAGRAM,
          username,
          paginationToken,
          currentCount,
          maxCount,
          total,
        }

        const task = await TaskService.getInstance().createTask(
          projectId,
          taskParams,
          user.id,
          reason || TaskReason.SEARCH,
          TaskType.FOLLOWING_LIST,
        )
        return reply.status(200).send(successResponse(task, 'success'))
      } catch (error) {
        if (error instanceof AssertionError) {
          return reply
            .status(400)
            .send(errorResponse(StatusCodes.BAD_REQUEST, 'VALIDATION_ERROR', error.message))
        }
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  // 获取 ins 的 following 结果
  fastify.get(
    '/followingList',
    {
      schema: {
        description: '获取 Instagram 关注列表结果',
        tags: ['Instagram'],
        security: [{ bearerAuth: [] }],
        querystring: PaginateReqSchema,
        // response: FullResponseSchema(),
      },
      ...withAuth(),
    },
    async (request: FastifyRequest, reply) => {
      const { projectId, page = 1, pageSize = 100 } = request.query as PaginateReq
      if (!projectId) {
        return reply.status(400).send(serverErrorResponse('projectId is required'))
      }
      if (isNaN(page) || isNaN(pageSize)) {
        return reply.status(400).send(serverErrorResponse('page and pageSize must be numbers'))
      }
      try {
        const result = await getProjectCandidatesWithPostsByTaskType(
          projectId,
          TaskType.FOLLOWING_LIST,
          KolPlatform.INSTAGRAM,
          {
            page,
            pageSize,
          },
        )
        return reply.status(200).send(successResponse(result))
      } catch (error) {
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  // instagram 的标记内容爆破
  fastify.post<{ Body: InsTaggedBreakReq }>(
    '/tagged',
    {
      schema: {
        description: '创建 Instagram 标记内容爆破任务',
        tags: ['Instagram'],
        security: [{ bearerAuth: [] }],
        body: InsTaggedBreakReqSchema,
        // response: FullResponseSchema(),
      },
      ...withAuth(
        dynamicCheckQuota(
          DynamicQuotaService.calculateTaggedBreakQuota,
          QuotaType.INS_TAGGED_BREAK,
        ),
      ),
    },
    async (req, reply) => {
      try {
        const { projectId, username, reason, maxVideoCount = 100 } = req.body
        let currentVideoCount = 0
        const user = (req as any).user

        assert(projectId, 'projectId is required!')
        assert(username && username.trim() !== '', 'username is required and cannot be empty!')

        const project = await prisma.project.findUnique({
          where: { id: projectId, deletedAt: null },
        })
        assert(project, 'Project not found or has been deleted!')

        let paginationToken: string = ''
        let total = 0
        if (reason !== TaskReason.SEARCH) {
          const projectCandidate = await prisma.projectCandidate.findUnique({
            where: {
              projectId_type: {
                projectId,
                type: TaskType.TAGGED_BREAK,
              },
            },
          })
          const meta = (projectCandidate?.meta as any) || {}
          paginationToken = meta.paginationToken || ''
          total = meta?.progress?.total || 0
          currentVideoCount = meta?.progress?.count || 0
        }

        const params: InsTaggedBreakTaskParams = {
          projectId,
          platform: KolPlatform.INSTAGRAM,
          username,
          paginationToken,
          currentVideoCount,
          maxVideoCount,
        }
        const task = await TaskService.getInstance().createTask(
          projectId,
          params,
          user.id,
          reason || TaskReason.SEARCH,
          TaskType.TAGGED_BREAK,
        )
        return reply.status(200).send(successResponse(task))
      } catch (error) {
        if (error instanceof AssertionError) {
          return reply
            .status(400)
            .send(errorResponse(StatusCodes.BAD_REQUEST, 'VALIDATION_ERROR', error.message))
        }
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  // 获取 ins 的 tagged 结果
  fastify.get(
    '/tagged',
    {
      schema: {
        description: '获取 Instagram 标记内容结果',
        tags: ['Instagram'],
        security: [{ bearerAuth: [] }],
        querystring: PaginateReqSchema,
        // response: FullResponseSchema(),
      },
      ...withAuth(),
    },
    async (request: FastifyRequest, reply) => {
      const { projectId, page = 1, pageSize = 100 } = request.query as PaginateReq
      if (!projectId) {
        return reply.status(400).send(serverErrorResponse('projectId is required'))
      }
      if (isNaN(page) || isNaN(pageSize)) {
        return reply.status(400).send(serverErrorResponse('page and pageSize must be numbers'))
      }
      try {
        const result = await getProjectCandidatesWithPostsByTaskType(
          projectId,
          TaskType.TAGGED_BREAK,
          KolPlatform.INSTAGRAM,
          {
            page,
            pageSize,
          },
        )
        return reply.status(200).send(successResponse(result))
      } catch (error) {
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  // 创建 ins 的 webList 任务
  fastify.post<{ Body: InsWebListReq }>(
    '/url-list',
    {
      schema: {
        summary: '[url-list]创建任务',
        description: '创建 Instagram URL 列表任务',
        tags: ['Instagram'],
        security: [{ bearerAuth: [] }],
        body: InsWebListReqSchema,
        // response: FullResponseSchema(),
      },
      ...withAuth(handleQuotaWithResponse(QuotaCost.INS_WEB_LIST, QuotaType.INS_WEB_LIST)),
    },
    async (req: FastifyRequest<{ Body: InsWebListReq }>, reply) => {
      try {
        const { projectId, urls, platform, reason } = req.body
        const user = (req as any).user
        assert(projectId, 'projectId is required!')
        assert(
          urls && Array.isArray(urls) && urls.length > 0,
          'urls is required and cannot be empty!',
        )
        assert(platform === KolPlatform.INSTAGRAM, 'platform must be INSTAGRAM!')
        assert(reason && reason === TaskReason.SEARCH, 'reason is invalid!')

        const project = await prisma.project.findUnique({
          where: { id: projectId, deletedAt: null },
        })
        assert(project, 'Project not found or has been deleted!')

        const params: InsWebListTaskParams = {
          projectId,
          platform,
          urls,
          reason: reason || TaskReason.SEARCH,
        }
        console.log(`[API] 创建 INS_WEB_LIST 任务 ${projectId}`, JSON.stringify(params))
        const task = await TaskService.getInstance().createTask(
          projectId,
          params,
          user.id,
          reason || TaskReason.SEARCH,
          TaskType.WEB_LIST,
        )
        return reply.status(200).send(successResponse(task))
      } catch (error) {
        if (error instanceof AssertionError) {
          return reply
            .status(400)
            .send(errorResponse(StatusCodes.BAD_REQUEST, 'VALIDATION_ERROR', error.message))
        }
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  // 假受众分析
  fastify.post<{
    Querystring: AudienceFakeReq
  }>(
    '/audience-fake',
    {
      schema: {
        summary: '创建假受众分析任务',
        description: '分析 Instagram 用户的假受众比例',
        tags: ['Instagram'],
        security: [{ bearerAuth: [] }],
        querystring: AudienceFakeReqSchema,
        response: FullResponseSchema(AudienceFakeResSchema),
      },
      ...withAuth(
        responseBasedQuota({
          checkQuota: (request) => {
            return QuotaCost.AUDIENCE_FAKE
          },
          calculateCost: (responseData, request) => {
            const fromCache = responseData?.data?.fromCache || false
            return fromCache ? 0 : QuotaCost.AUDIENCE_FAKE
          },
          quotaType: QuotaType.AUDIENCE_FAKE,
          extractMetadata: (responseData, request) => {
            const { handler } = request.query as any
            const task = responseData?.data?.task || {}
            return {
              handler,
              taskId: task.id,
              fromCache: responseData?.data?.fromCache || false,
              taskType: TaskType.AUDIENCE_FAKE,
            }
          },
        }),
      ),
    },
    async (request, reply) => {
      const { handler } = request.query
      const user = (request as any).user

      const oneMonthAgo = new Date()
      oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1)

      const existingTask = await prisma.similarChannelTask.findFirst({
        where: {
          type: TaskType.AUDIENCE_FAKE,
          createdAt: { gte: oneMonthAgo },
          params: { path: ['handler'], equals: handler },
          result: { not: { equals: null } },
          status: SimilarChannelTaskStatus.COMPLETED,
        },
        orderBy: { createdAt: 'desc' },
      })

      if (existingTask?.result) {
        console.log(`[audience-fake] 找到缓存任务: ${existingTask.id}`)
        return reply.send(successResponse({ task: existingTask, fromCache: true }))
      }

      const task = await TaskService.getInstance().createTask(
        `AudienceFake:${handler}`,
        { handler, platform: KolPlatform.INSTAGRAM },
        user.id,
        TaskReason.AUDIENCE_FAKE,
        TaskType.AUDIENCE_FAKE,
      )

      return reply.send(successResponse({ task, fromCache: false }))
    },
  )

  // 导出假受众分析 Excel
  fastify.get<{
    Querystring: AudienceFakeExportReq
  }>(
    '/audience-fake/export',
    {
      schema: {
        summary: '导出假受众分析 Excel',
        description: '根据任务ID导出假受众分析的详细Excel报告',
        tags: ['Instagram'],
        security: [{ bearerAuth: [] }],
        querystring: AudienceFakeExportReqSchema,
        response: FullResponseSchema(AudienceFakeExportResSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { taskId } = request.query
      const result = await InsInfoService.exportAudienceFakeExcel(taskId)
      return reply.send(successResponse(result))
    },
  )
}

export default router
