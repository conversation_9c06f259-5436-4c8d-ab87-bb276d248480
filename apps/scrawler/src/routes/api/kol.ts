import { uploadFileByBuffer } from '@/api/aliyun'
import { handleUnknownError } from '@/common/errorHandler'
import {
  StatusCodes,
  errorResponse,
  serverErrorResponse,
  successResponse,
} from '@/common/response/response'
import { HOST } from '@/config/env'
import {
  commonDebugColumns,
  likesKolColumns,
  similarInstagramColumns,
  similarTikTokColumns,
  similarTwitterColumns,
  similarYouTubeColumns,
} from '@/config/exportColumns'
import { CommonResponseSchema, FullResponseSchema } from '@/config/swagger'
import { QuotaCost } from '@/enums/QuotaCost'
import Sentry from '@/infras/sentry'
import { isAllowedEmail, needSupabaseAuth, withAuth } from '@/middlewares/auth'
import { monthlyRateLimit } from '@/middlewares/monthlyRateLimit'
import { platformRateLimit } from '@/middlewares/platformRateLimit'
import { checkQuota } from '@/middlewares/quota'
import { freeUserLimit } from '@/middlewares/userTypeLimit'
import EmailService from '@/services/email'
import {
  commonStyle,
  exportUserLikesToOSS,
  formatInstagramSearchDataForExport,
  formatTikTokSearchDataForExport,
  formatTwitterSearchDataForExport,
  formatYouTubeSearchDataForExport,
  generateKolExportData,
} from '@/services/export.service'
import KolService from '@/services/kol'
import { addEmailForKol, getKolRegionInfoByIdentifier } from '@/services/kolInfo.service'
import { getKolRelationForUser } from '@/services/kolRelation'
import { MembershipService } from '@/services/membership.service'
import { getProjectCandidates } from '@/services/project'
import { findCurrentTasksByProjectId } from '@/services/similar'
import { TimezoneService } from '@/services/timezone.service'
import { getUserByUserId } from '@/services/user'
import { EmailSourceType } from '@/types/email'
import { createTaskRequest } from '@/types/request/similar.request'
import { KolInfoWithScore } from '@/types/response/union-search.response'
import { SheetData, exportToExcel } from '@/utils/excel'
import { waitFor } from '@/utils/lock'
import { EmailVerifyStatus, KolPlatform, QuotaType, TaskType, prisma } from '@repo/database'
import assert from 'assert'
import { FastifyPluginAsync, FastifyRequest } from 'fastify'
import {
  ExportRequest,
  ExportRequestSchema,
  GetKolIdRequest,
  GetKolIdRequestSchema,
  GetKolIdResponseSchema,
} from '../schemas/kol'

const router: FastifyPluginAsync = async (fastify, opts): Promise<void> => {
  fastify.get(
    '/:id',
    {
      preHandler: needSupabaseAuth,
    },
    async (request, reply) => {
      const { id } = request.params as any
      const kol = await prisma.kolInfo.findFirst({
        where: {
          id,
        },
      })
      if (!kol) {
        return reply.status(404).send({ error: 'KOL not found' })
      }
      return reply.send(kol)
    },
  )

  fastify.patch(
    '/:id',
    {
      preHandler: needSupabaseAuth,
    },
    async (request, reply) => {
      const { id } = request.params as any
      const { user } = request as any
      let { email, emailSource } = request.body as any
      if (!email?.length) {
        return errorResponse(StatusCodes.EMAIL_REQUIRED, 'Email is required')
      }
      if (!emailSource) {
        emailSource = EmailSourceType.REVEAL_BUTTON
      }
      if (emailSource == EmailSourceType.USER_SUBMIT) {
        const { user } = request as any
        await prisma.userSubmitEmail.create({
          data: {
            userId: user.id,
            kolId: id,
            email: email,
            status: EmailVerifyStatus.PENDING,
          },
        })
        return reply.status(200).send('success')
      }
      const kol = await addEmailForKol(user.id, id, email, emailSource)
      return reply.send(kol)
    },
  )

  fastify.post(
    '/:id/fetchEmail',
    {
      preHandler: needSupabaseAuth,
    },
    async (request, reply) => {
      const { id } = request.params as any
      const kol = await prisma.kolInfo.findFirst({
        where: {
          id,
        },
      })
      const { user } = request as any
      if (!kol) {
        return reply.status(404).send({
          errCode: 'KOL_NOT_FOUND',
          error: 'KOL not found',
        })
      }
      const targetEmail = await EmailService.getInstance().getKolEmail(user.id, kol)
      if (!targetEmail.email) {
        return reply.status(404).send({
          errCode: 'KOL_EMAIL_NOT_FOUND',
          error: 'KOL Email not found',
        })
      }
      await addEmailForKol(user.id, kol.id, targetEmail.email, targetEmail.source)
      const fetchedKol = await prisma.kolInfo.findFirst({
        where: {
          id,
        },
      })

      return reply.send(fetchedKol)
    },
  )

  fastify.post<{ Body: ExportRequest }>(
    '/export',
    {
      schema: {
        tags: ['kol'],
        summary: '导出KOL数据',
        description: '导出项目的KOL数据到Excel文件',
        body: ExportRequestSchema,
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { projectId, isWeb } = request.body
      const { user } = request as any
      const project = await prisma.project.findUniqueOrThrow({
        where: {
          id: projectId,
          deletedAt: null,
        },
      })

      // wait for email fulfilling task
      await waitFor(KolService.getInstance().getFulfillEmailInfoTaskKey(projectId))

      const membership = await MembershipService.getInstance().getMembership(user.id)

      const isDebug = await isAllowedEmail(user.id)
      // ins and ytb get the latest task
      const similarTasks = await findCurrentTasksByProjectId(projectId, TaskType.SIMILAR)
      const similarLatestTask = similarTasks
        .filter((t) => t.isCompleted && t.type === TaskType.SIMILAR)
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())[0]
      const similarTaskParams = similarLatestTask?.params as createTaskRequest

      const kolExportData = await generateKolExportData(projectId, membership)
      const sheets: SheetData[] = []

      const sheetMap: {
        likes?: SheetData
        similar?: SheetData
      } = {}
      sheetMap.likes = {
        name: 'Likes',
        data: kolExportData,
        columns: likesKolColumns,
        styles: commonStyle,
      }

      // 处理相似待筛选数据
      if (similarLatestTask && similarTaskParams && !isWeb) {
        switch (similarTaskParams.platform) {
          case KolPlatform.TIKTOK: {
            const unionSearchData: KolInfoWithScore[] = await getProjectCandidates(
              projectId,
              TaskType.SIMILAR,
              KolPlatform.TIKTOK,
            )
            console.log(`unionSearchData.length: ${unionSearchData.length}`)
            const formattedUnionSearchData = await formatTikTokSearchDataForExport(unionSearchData)
            const tiktokSimilarSheet = {
              name: 'Similar Pending Filtering',
              data: formattedUnionSearchData,
              columns: isDebug
                ? [...similarTikTokColumns, ...commonDebugColumns]
                : similarTikTokColumns,
              styles: commonStyle,
            }
            sheetMap.similar = tiktokSimilarSheet
            break
          }
          case KolPlatform.YOUTUBE: {
            const data: KolInfoWithScore[] = await getProjectCandidates(
              projectId,
              TaskType.SIMILAR,
              KolPlatform.YOUTUBE,
            )
            const formattedData = await formatYouTubeSearchDataForExport(data)

            const youtubeSimilarSheet = {
              name: 'Similar Pending Filtering',
              data: formattedData,
              columns: isDebug
                ? [...similarYouTubeColumns, ...commonDebugColumns]
                : similarYouTubeColumns,
              styles: commonStyle,
            }
            sheetMap.similar = youtubeSimilarSheet
            break
          }
          case KolPlatform.INSTAGRAM: {
            const data: KolInfoWithScore[] = await getProjectCandidates(
              projectId,
              TaskType.SIMILAR,
              KolPlatform.INSTAGRAM,
            )
            const formatData = await formatInstagramSearchDataForExport(data)
            const insSimilarSheet = {
              name: 'Similar Pending Filtering',
              data: formatData,
              columns: isDebug
                ? [...similarInstagramColumns, ...commonDebugColumns]
                : similarInstagramColumns,
              styles: commonStyle,
            }
            sheetMap.similar = insSimilarSheet
            break
          }
          case KolPlatform.TWITTER: {
            const data: KolInfoWithScore[] = await getProjectCandidates(
              projectId,
              TaskType.SIMILAR,
              KolPlatform.TWITTER,
            )
            const formatData = await formatTwitterSearchDataForExport(data)
            const twitterSimilarSheet = {
              name: 'Similar Pending Filtering',
              data: formatData,
              columns: isDebug
                ? [...similarTwitterColumns, ...commonDebugColumns]
                : similarTwitterColumns,
              styles: commonStyle,
            }
            sheetMap.similar = twitterSimilarSheet
            break
          }
          default:
            throw new Error('Unsupported platform')
        }
      }

      // 按照指定顺序添加存在的sheet
      if (sheetMap.similar) sheets.push(sheetMap.similar)
      if (sheetMap.likes) sheets.push(sheetMap.likes)

      const buffer = await exportToExcel(sheets)
      const dateStr = TimezoneService.formatToUserTimezone(
        new Date(),
        membership?.timezone || 'Asia/Shanghai',
      )
        .slice(0, 19)
        .replace(/\s+/g, '_') // 替换空格为下划线
        .replace(/[T:]/g, '-')

      const projectTitle = project?.title || 'export'

      const sanitizeFileName = (name: string) => {
        return name
          .replace(/[<>:"/\\|?*#]/g, '_') // 替换Windows非法字符和URL特殊字符
          .replace(/\s+/g, '_') // 替换空格为下划线

          .replace(/_+/g, '_') // 将多个连续下划线替换为单个
          .trim()
      }

      const userInfo = await getUserByUserId(user.id)
      const userEmail = userInfo.email || ''

      // 在文件名末尾添加用户邮箱
      const fileName = `${sanitizeFileName(projectTitle)}_${dateStr}_${sanitizeFileName(userEmail)}.xlsx`
      const filePath = `excel/${fileName}`
      await uploadFileByBuffer(filePath, buffer)
      return reply.send({
        url: `${HOST ?? ''}/files/excel/${fileName}`,
      })
    },
  )

  fastify.get(
    '/info/search',
    withAuth(checkQuota(QuotaCost.CARD_QUERY, QuotaType.CARD_QUERY)),
    async (request, reply) => {
      const { handler, id, platform } = request.query as any

      if (!handler && !id) {
        return reply.status(400).send({ error: 'handler or id must need one' })
      }
      const { user } = request as any
      try {
        const kolService = KolService.getInstance()
        const kol = await kolService.findOrCreateKol(user.id, handler, id, platform as KolPlatform)
        if (!kol) {
          return reply.status(404).send({ error: 'KOL not found' })
        }

        // 并行
        const [_, platformInfo] = await Promise.all([
          kolService.tryPatchKolEmail(user.id, kol),
          kolService.getPlatformSpecificInfo(kol, platform, handler),
        ])

        const response = {
          kolInfo: {
            id: kol.id,
            email: kol.email,
            emailSource: kol.emailSource,
            description: kol.description,
            platformAccount: kol.platformAccount,
            platform: kol.platform,
          },
          ...platformInfo,
        }
        return reply.send(successResponse(response, 'success'))
      } catch (error) {
        reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  // 获取信息卡片邮箱信息
  fastify.get(
    '/info/email',
    withAuth(
      checkQuota(async (request: FastifyRequest) => {
        const { user } = request as any
        const membership = await MembershipService.getInstance().getMembership(user.id)
        if (membership) {
          const hasCardSubscription =
            await MembershipService.getInstance().checkMemberCardSubscription(membership)
          if (hasCardSubscription) {
            return QuotaCost.CARD_QUERY_HAS_SUBSCRIPTION
          }
        }
        return QuotaCost.CARD_QUERY
      }),
    ),
    async (request, reply) => {
      const { handler, id, platform } = request.query as any

      if (!handler && !id) {
        return reply.status(400).send({ error: 'handler or id must need one' })
      }
      const { user } = request as any
      try {
        const kolService = KolService.getInstance()
        const kol = await kolService.findOrCreateKol(user.id, handler, id, platform as KolPlatform)
        if (!kol) {
          return reply.status(404).send({ error: 'KOL not found' })
        }

        await kolService.tryPatchKolEmail(user.id, kol)

        const response = {
          kolInfo: {
            id: kol.id,
            email: kol.email,
            platformAccount: kol.platformAccount,
            platform: kol.platform,
          },
        }
        return reply.send(successResponse(response, 'success'))
      } catch (error) {
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  fastify.get<{
    Querystring: {
      handler?: string
      id?: string
      platform: KolPlatform
    }
  }>(
    '/info/platform',
    withAuth(
      monthlyRateLimit(),
      checkQuota(async (request: FastifyRequest) => {
        const { user } = request as any
        const membership = await MembershipService.getInstance().getMembership(user.id)

        if (membership) {
          const hasCardSubscription =
            await MembershipService.getInstance().checkMemberCardSubscription(membership)
          if (hasCardSubscription) {
            return QuotaCost.CARD_QUERY_HAS_SUBSCRIPTION
          }
        }
        return QuotaCost.CARD_QUERY
      }, QuotaType.CARD_QUERY),
    ),
    async (request, reply) => {
      const { handler, id, platform } = request.query as any
      assert(handler || id, 'handler or id must need one')
      assert(Object.values(KolPlatform).includes(platform), 'platform is required')

      const kolService = KolService.getInstance()
      const { user } = request as any
      const kol = await kolService.findOrCreateKol(user.id, handler, id, platform as KolPlatform)
      assert(kol, 'KOL not found')

      const platformInfo = await kolService.getPlatformSpecificInfo(kol, platform, handler)

      const response = {
        kolInfo: {
          id: kol.id,
          email: kol.email,
          description: kol.description,
          platformAccount: kol.platformAccount,
          platform: kol.platform,
          contacts: kol.contacts,
        },
        ...platformInfo,
      }
      return reply.send(successResponse(response, 'success'))
    },
  )

  fastify.get<{
    Querystring: {
      handler?: string
      id?: string
      platform: KolPlatform
    }
  }>(
    '/info/platform-v2',
    {
      preHandler: platformRateLimit(),
    },
    async (request, reply) => {
      const { handler, id, platform } = request.query as any
      assert(handler || id, 'handler or id must need one')
      assert(Object.values(KolPlatform).includes(platform), 'platform is invalid')

      const kolService = KolService.getInstance()
      const kol = await kolService.findOrCreateKol(undefined, handler, id, platform as KolPlatform)
      assert(kol, 'KOL not found')

      const [platformInfo, _] = await Promise.all([
        kolService.getPlatformSpecificInfo(kol, platform, handler),
        kolService.tryPatchKolEmail(undefined, kol),
      ])

      const response = {
        platform: kol.platform,
        platformAccount: kol.platformAccount,
        email: kol.email,
        region: platformInfo.region,
        socialMediaIds: platformInfo.socialMediaIds,
      }
      return reply.send(successResponse(response, 'success'))
    },
  )

  fastify.get('/info/region', withAuth(), async (request, reply) => {
    const { handler, id, platform } = request.query as {
      handler?: string
      id?: string
      platform?: KolPlatform
    }

    if (!handler && !id) {
      return reply.status(400).send({ error: 'handler or id must need one' })
    }

    if (!platform || !Object.values(KolPlatform).includes(platform)) {
      return reply.status(400).send({ error: 'Invalid platform parameter' })
    }

    try {
      const kolDetailInfo = await getKolRegionInfoByIdentifier({ handler, id }, platform)
      return reply.status(200).send(successResponse(kolDetailInfo))
    } catch (error: any) {
      console.error(`获取KOL区域信息失败: ${error.message}`, error)
      Sentry.captureException(error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.get('/info/relation', withAuth(), async (request, reply) => {
    const { user } = request as any
    const { kolId } = (request as any).query

    if (!kolId) {
      return reply.status(400).send({ error: 'kolId is required' })
    }

    try {
      const response = await getKolRelationForUser(kolId, user.id)
      return reply.status(200).send(successResponse(response))
    } catch (error) {
      console.error('getKolRelationForUser error', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.get('/info/relation-v2', withAuth(), async (request, reply) => {
    const { handler, id, platform } = request.query as any
    const { user } = request as any
    if (!handler && !id) {
      return reply.status(400).send({ error: 'handler or id must need one' })
    }
    try {
      const kolService = KolService.getInstance()
      const kol = await kolService.findOrCreateKol(user.id, handler, id, platform as KolPlatform)
      if (!kol) {
        return reply.status(404).send({ error: 'KOL not found' })
      }
      const response = await getKolRelationForUser(kol.id, user.id)
      return reply.status(200).send(successResponse(response))
    } catch (error) {
      console.error('getKolRelationForUser error', error)
      return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
    }
  })

  fastify.patch('/links', withAuth(), async (request, reply) => {
    const { links, kolId } = request.body as any
    const { user } = request as any
    if (!links?.length || !(links instanceof Array) || !kolId?.length) {
      return reply.status(400).send(errorResponse(StatusCodes.BAD_REQUEST, 'BAD_REQUEST'))
    }
    try {
      const contacts = await KolService.getInstance().updateLinks(user.id, kolId, links)
      console.log(
        `[external-link]update links for ${kolId} with ${links.length} links, get ${contacts.length} results: ${contacts}`,
      )
      return reply.status(200).send(successResponse(contacts))
    } catch (err: any) {
      return reply
        .status(500)
        .send(
          errorResponse(
            StatusCodes.SERVER_ERROR,
            'SERVER_ERROR',
            err?.message ? err.message : JSON.stringify(err),
          ),
        )
    }
  })

  // 导出用户所有的 like 和 superlike 数据
  fastify.post(
    '/export/user-likes',
    {
      schema: {
        summary: '导出Like/Superlike',
        description: '导出用户所有的 like 和 superlike 数据',
        tags: ['export'],
        response: CommonResponseSchema,
      },
      ...withAuth(freeUserLimit()),
    },
    async (request, reply) => {
      const { user } = request as any

      try {
        const membership = await MembershipService.getInstance().getMembership(user.id)

        // 流式导出到OSS
        const fileName = await exportUserLikesToOSS(user.id, membership)

        // 返回下载链接
        return reply.status(200).send(
          successResponse({
            message: 'Export completed successfully',
            url: `${HOST ?? ''}/files/excel/${fileName.replace('excel/', '')}`,
          }),
        )
      } catch (error) {
        console.error('[Export User Likes] Error during export process:', error)
        Sentry.captureException(error)
        return reply.status(500).send(serverErrorResponse(handleUnknownError(error)))
      }
    },
  )

  fastify.get(
    '/id',
    {
      schema: {
        summary: '获取KOL ID',
        description: '获取KOL ID',
        tags: ['kols'],
        querystring: GetKolIdRequestSchema,
        response: FullResponseSchema(GetKolIdResponseSchema),
      },
      ...withAuth(),
    },
    async (request, reply) => {
      const { platform, platformAccount } = request.query as GetKolIdRequest
      const kolInfo = await KolService.getInstance().getKolId(platform, platformAccount)
      return reply.send(successResponse(kolInfo))
    },
  )
}

export default router
