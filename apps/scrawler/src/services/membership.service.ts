import { throwError } from '@/common/errors/statusCodes'
import { ApiResponse, StatusCodes, errorResponse } from '@/common/response/response'
import {
  FREE_DEFAULT_ACCOUNT_QUOTA,
  MIN_COST_FOR_EXCLUDE_LINK,
  PRICE_FOR_EXCLUDE_LINK,
} from '@/config/env.ts'
import { QuotaCost, quotaCostMap } from '@/enums/QuotaCost'
import Sentry from '@/infras/sentry'
import {
  DailyQuotaResult,
  DateFilter,
  DeductDynamicQuotaParams,
  DeductQuotaParams,
  ERROR_MESSAGES,
  FREE_QUOTA_STRATEGY,
  MemberShipEnterpriseInfo,
  MemberShipInfo,
  QuotaDetailResult,
  UpdateMembershipParams,
  UserMembership,
} from '@/types/memberShip'
import {
  CardSubscriptionStatus,
  EnterpriseStatus,
  MemberStatus,
  MemberType,
  QuotaLog,
  QuotaType,
  prisma,
} from '@repo/database'
import assert from 'assert'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone.js'
import utc from 'dayjs/plugin/utc.js'
import { TimezoneService } from './timezone.service'
import { hasVisitedInLastWeek } from './visit.service'
dayjs.extend(utc)
dayjs.extend(timezone)

export class MembershipService {
  private static instance: MembershipService
  private constructor() {}

  public static getInstance(): MembershipService {
    if (!MembershipService.instance) {
      MembershipService.instance = new MembershipService()
    }
    return MembershipService.instance
  }

  async getMembership(userId: string) {
    if (!userId) {
      return null
    }
    return await prisma.userMembership.findUnique({
      where: { userId },
      include: {
        enterprise: true,
      },
    })
  }

  async getUserQuota(userId: string) {
    const membership = await prisma.userMembership.findUnique({
      where: { userId },
      include: {
        enterprise: true,
      },
    })
    if (!membership) {
      throw new Error(ERROR_MESSAGES.MEMBERSHIP_NOT_FOUND)
    }

    if (membership.status === MemberStatus.SUSPENDED) {
      throw new Error(ERROR_MESSAGES.MEMBERSHIP_SUSPENDED)
    }

    if (membership.status === MemberStatus.DEVICE_BAN) {
      throw new Error(ERROR_MESSAGES.MEMBERSHIP_DEVICE_BAN)
    }

    const needsResetDailyUsage = TimezoneService.isNewDay(
      membership.lastResetAt,
      membership.timezone,
    )

    if (needsResetDailyUsage) {
      await prisma.$transaction(async (tx) => {
        await tx.userMembership.update({
          where: { id: membership.id },
          data: {
            dailyUsage: 0,
            lastResetAt: new Date(),
          },
        })
      })
    }

    const userTimezone = membership.timezone || 'Asia/Shanghai'
    const { start: todayInUserTz, end: endOfDayInUserTz } =
      TimezoneService.getUserDayRange(userTimezone)

    // 处理不同类型的会员的状态
    switch (membership.type) {
      case MemberType.ENTERPRISE:
        if (!membership.enterpriseId || !membership.enterprise) {
          throw new Error(ERROR_MESSAGES.ENTERPRISE_NOT_FOUND)
        }
        if (membership.enterprise.status === EnterpriseStatus.SUSPENDED) {
          throw new Error(ERROR_MESSAGES.ENTERPRISE_SUSPENDED)
        }

        if (
          membership.enterprise?.expireAt &&
          membership.enterprise.expireAt < dayjs.utc().toDate() &&
          membership.enterprise.status !== EnterpriseStatus.EXPIRED
        ) {
          // 企业过期配额不清空，只更新企业账号状态,过期时间不刷新需练习管理员
          await prisma.$transaction(
            async (tx) => {
              await tx.enterprise.update({
                where: { id: membership.enterpriseId! },
                data: { status: EnterpriseStatus.EXPIRED },
              })
              await tx.quotaLog.create({
                data: {
                  userId,
                  membershipId: membership.id,
                  enterpriseId: membership.enterpriseId,
                  usage: 0,
                  type: QuotaType.EXPIRE,
                  description: 'Enterprise membership expired',
                  createdBy: 'system',
                  metadata: {
                    timezone: userTimezone,
                    convertedTime: dayjs.utc().toISOString(),
                  },
                },
              })
            },
            { timeout: 15_000 },
          )
        }
        break

      case MemberType.PAID:
        if (membership.expireAt && membership.expireAt < dayjs.utc().toDate()) {
          await prisma.$transaction(
            async (tx) => {
              const expiredQuota = membership.accountQuota - membership.usedQuota
              const daysSinceCreation = dayjs().diff(dayjs(membership.createdAt), 'days')
              let todayQuota = FREE_QUOTA_STRATEGY.REGULAR
              if (daysSinceCreation === 0) {
                todayQuota = FREE_QUOTA_STRATEGY.FIRST_DAY
              } else if (daysSinceCreation <= FREE_QUOTA_STRATEGY.FIRST_WEEK_DAYS) {
                todayQuota = FREE_QUOTA_STRATEGY.FIRST_WEEK
              } else if (daysSinceCreation <= FREE_QUOTA_STRATEGY.AFTER_FIRST_MONTH_DAYS) {
                todayQuota = FREE_QUOTA_STRATEGY.FIRST_MONTH
              }
              await tx.userMembership.update({
                where: { id: membership.id },
                data: {
                  type: MemberType.FREE,
                  accountQuota: todayQuota,
                  usedQuota: 0,
                  effectiveAt: todayInUserTz,
                  expireAt: endOfDayInUserTz,
                },
              })

              await tx.quotaLog.create({
                data: {
                  userId,
                  membershipId: membership.id,
                  usage: +FREE_DEFAULT_ACCOUNT_QUOTA,
                  type: QuotaType.EXPIRE,
                  description: `Member expired downgrade,expired quota:${expiredQuota}, downgrade to free`,
                  createdBy: 'system',
                  metadata: {
                    timezone: userTimezone,
                    convertedTime: dayjs.utc().toISOString(),
                  },
                },
              })
            },
            { timeout: 15000 },
          )
        }
        break
      case MemberType.FREE:
        const needsReset = TimezoneService.isNewDay(membership.effectiveAt, userTimezone)
        if (needsReset) {
          await prisma.$transaction(
            async (tx) => {
              const daysSinceCreation = dayjs().diff(dayjs(membership.createdAt), 'days')
              let todayQuota = FREE_QUOTA_STRATEGY.REGULAR
              if (daysSinceCreation === 0) {
                todayQuota = FREE_QUOTA_STRATEGY.FIRST_DAY
              } else if (daysSinceCreation <= FREE_QUOTA_STRATEGY.FIRST_WEEK_DAYS) {
                todayQuota = FREE_QUOTA_STRATEGY.FIRST_WEEK
              } else if (daysSinceCreation <= FREE_QUOTA_STRATEGY.AFTER_FIRST_MONTH_DAYS) {
                todayQuota = FREE_QUOTA_STRATEGY.FIRST_MONTH
              }

              await tx.userMembership.update({
                where: { id: membership.id },
                data: {
                  accountQuota: todayQuota,
                  usedQuota: 0,
                  effectiveAt: todayInUserTz,
                  expireAt: endOfDayInUserTz,
                },
              })

              await tx.quotaLog.create({
                data: {
                  userId,
                  membershipId: membership.id,
                  usage: todayQuota,
                  type: QuotaType.RESET,
                  description: `Daily quota reset (${
                    daysSinceCreation === 0
                      ? 'First day'
                      : daysSinceCreation <= FREE_QUOTA_STRATEGY.FIRST_WEEK_DAYS
                        ? 'First week'
                        : daysSinceCreation <= FREE_QUOTA_STRATEGY.AFTER_FIRST_MONTH_DAYS
                          ? 'First month'
                          : 'Regular'
                  })`,
                  createdBy: 'system',
                  metadata: {
                    timezone: userTimezone,
                    convertedTime: dayjs.utc().toISOString(),
                    daysSinceCreation,
                    quotaStrategy:
                      daysSinceCreation === 0
                        ? 'FIRST_DAY'
                        : daysSinceCreation <= FREE_QUOTA_STRATEGY.FIRST_WEEK_DAYS
                          ? 'FIRST_WEEK'
                          : daysSinceCreation <= FREE_QUOTA_STRATEGY.AFTER_FIRST_MONTH_DAYS
                            ? 'FIRST_MONTH'
                            : 'REGULAR',
                  },
                },
              })
            },
            { timeout: 15000 },
          )
        }
        break

      default:
        throw new Error('Invalid membership type')
    }

    return await prisma.userMembership.findUnique({
      where: { userId },
      include: {
        enterprise: true,
      },
    })
  }

  // only use in payment to reset user quota only in free and paid
  async refreshUserStatusInPayMent(userId: string) {
    const membership = await prisma.userMembership.findUniqueOrThrow({
      where: { userId },
    })

    const needsResetDailyUsage = TimezoneService.isNewDay(
      membership.lastResetAt,
      membership.timezone,
    )

    if (needsResetDailyUsage) {
      await prisma.$transaction(async (tx) => {
        await tx.userMembership.update({
          where: { id: membership.id },
          data: {
            dailyUsage: 0,
            lastResetAt: new Date(),
          },
        })
      })
    }

    const userTimezone = membership.timezone || 'Asia/Shanghai'
    const { start: todayInUserTz, end: endOfDayInUserTz } =
      TimezoneService.getUserDayRange(userTimezone)

    // 处理不同类型的会员的状态
    switch (membership.type) {
      case MemberType.PAID:
        if (membership.expireAt && membership.expireAt < dayjs.utc().toDate()) {
          await prisma.$transaction(
            async (tx) => {
              const expiredQuota = membership.accountQuota - membership.usedQuota
              const daysSinceCreation = dayjs().diff(dayjs(membership.createdAt), 'days')
              let todayQuota = FREE_QUOTA_STRATEGY.REGULAR
              if (daysSinceCreation === 0) {
                todayQuota = FREE_QUOTA_STRATEGY.FIRST_DAY
              } else if (daysSinceCreation <= FREE_QUOTA_STRATEGY.FIRST_WEEK_DAYS) {
                todayQuota = FREE_QUOTA_STRATEGY.FIRST_WEEK
              } else if (daysSinceCreation <= FREE_QUOTA_STRATEGY.AFTER_FIRST_MONTH_DAYS) {
                todayQuota = FREE_QUOTA_STRATEGY.FIRST_MONTH
              }
              await tx.userMembership.update({
                where: { id: membership.id },
                data: {
                  type: MemberType.FREE,
                  accountQuota: todayQuota,
                  usedQuota: 0,
                  effectiveAt: todayInUserTz,
                  expireAt: endOfDayInUserTz,
                },
              })

              await tx.quotaLog.create({
                data: {
                  userId,
                  membershipId: membership.id,
                  usage: +FREE_DEFAULT_ACCOUNT_QUOTA,
                  type: QuotaType.EXPIRE,
                  description: `Member expired downgrade,expired quota:${expiredQuota}, downgrade to free`,
                  createdBy: 'system',
                  metadata: {
                    timezone: userTimezone,
                    convertedTime: dayjs.utc().toISOString(),
                  },
                },
              })
            },
            { timeout: 10_000 },
          )
        }
        break
      case MemberType.FREE:
        const needsReset = TimezoneService.isNewDay(membership.effectiveAt, userTimezone)
        if (needsReset) {
          await prisma.$transaction(
            async (tx) => {
              const daysSinceCreation = dayjs().diff(dayjs(membership.createdAt), 'days')
              let todayQuota = FREE_QUOTA_STRATEGY.REGULAR
              if (daysSinceCreation === 0) {
                todayQuota = FREE_QUOTA_STRATEGY.FIRST_DAY
              } else if (daysSinceCreation <= FREE_QUOTA_STRATEGY.FIRST_WEEK_DAYS) {
                todayQuota = FREE_QUOTA_STRATEGY.FIRST_WEEK
              } else if (daysSinceCreation <= FREE_QUOTA_STRATEGY.AFTER_FIRST_MONTH_DAYS) {
                todayQuota = FREE_QUOTA_STRATEGY.FIRST_MONTH
              }

              await tx.userMembership.update({
                where: { id: membership.id },
                data: {
                  accountQuota: todayQuota,
                  usedQuota: 0,
                  effectiveAt: todayInUserTz,
                  expireAt: endOfDayInUserTz,
                },
              })

              await tx.quotaLog.create({
                data: {
                  userId,
                  membershipId: membership.id,
                  usage: todayQuota,
                  type: QuotaType.RESET,
                  description: `Daily quota reset (${
                    daysSinceCreation === 0
                      ? 'First day'
                      : daysSinceCreation <= FREE_QUOTA_STRATEGY.FIRST_WEEK_DAYS
                        ? 'First week'
                        : daysSinceCreation <= FREE_QUOTA_STRATEGY.AFTER_FIRST_MONTH_DAYS
                          ? 'First month'
                          : 'Regular'
                  })`,
                  createdBy: 'system',
                  metadata: {
                    timezone: userTimezone,
                    convertedTime: dayjs.utc().toISOString(),
                    daysSinceCreation,
                    quotaStrategy:
                      daysSinceCreation === 0
                        ? 'FIRST_DAY'
                        : daysSinceCreation <= FREE_QUOTA_STRATEGY.FIRST_WEEK_DAYS
                          ? 'FIRST_WEEK'
                          : daysSinceCreation <= FREE_QUOTA_STRATEGY.AFTER_FIRST_MONTH_DAYS
                            ? 'FIRST_MONTH'
                            : 'REGULAR',
                  },
                },
              })
            },
            { timeout: 10_000 },
          )
        }
        break
      case MemberType.ENTERPRISE:
        throw new Error('enterprise member should not be in payment ')
      default:
        throw new Error('Invalid membership type')
    }

    return await prisma.userMembership.findUniqueOrThrow({
      where: { userId },
    })
  }

  /**
   * 检查会员状态
   * @param membership 会员信息
   * @param required 需要的配额数量
   * @throws Error 当检查不通过时抛出对应错误
   */
  public checkMembershipStatus(membership: UserMembership, required?: number) {
    if (!membership) {
      throwError(StatusCodes.MEMBERSHIP_NOT_FOUND)
    }
    if (membership.status === MemberStatus.SUSPENDED) {
      throwError(StatusCodes.MEMBERSHIP_SUSPENDED)
    }

    if (membership.status === MemberStatus.DEVICE_BAN) {
      throwError(StatusCodes.DEVICE_BAN)
    }

    const now = dayjs.utc().toDate()

    if (!membership.effectiveAt || membership.effectiveAt > now) {
      throwError(StatusCodes.MEMBERSHIP_NOT_EFFECTIVE)
    }

    if (!membership.expireAt || membership.expireAt < now) {
      throwError(StatusCodes.MEMBERSHIP_EXPIRED)
    }

    let remaining = 0
    switch (membership.type) {
      case MemberType.ENTERPRISE:
        if (!membership.enterprise) {
          throwError(StatusCodes.ENTERPRISE_NOT_FOUND)
        }
        if (membership.enterprise.status === EnterpriseStatus.SUSPENDED) {
          throwError(StatusCodes.ENTERPRISE_SUSPENDED)
        }
        if (membership.enterprise.status === EnterpriseStatus.EXPIRED) {
          throwError(StatusCodes.ENTERPRISE_EXPIRED)
        }
        if (!membership.enterprise.effectiveAt || membership.enterprise.effectiveAt > now) {
          throwError(StatusCodes.ENTERPRISE_NOT_EFFECTIVE)
        }
        if (!membership.enterprise.expireAt || membership.enterprise.expireAt < now) {
          throwError(StatusCodes.ENTERPRISE_EXPIRED)
        }

        if (required) {
          const enterpriseRemaining =
            membership.enterprise.accountQuota - membership.enterprise.usedQuota
          if (enterpriseRemaining <= 0) {
            throwError(StatusCodes.ENTERPRISE_QUOTA_EXCEEDED)
          }
          if (enterpriseRemaining < required) {
            throwError(StatusCodes.ENTERPRISE_INSUFFICIENT_QUOTA)
          }
        }
        break
      case MemberType.PAID:
        remaining = membership.accountQuota - membership.usedQuota
        if (required) {
          if (remaining <= 0) {
            throwError(StatusCodes.PAID_QUOTA_EXCEEDED)
          }
          if (remaining < required) {
            throwError(StatusCodes.PAID_INSUFFICIENT_QUOTA)
          }
        }
        break
      case MemberType.FREE:
        remaining = membership.accountQuota - membership.usedQuota
        if (required) {
          if (remaining <= 0) {
            throwError(StatusCodes.FREE_QUOTA_EXCEEDED)
          }
          if (remaining < required) {
            throwError(StatusCodes.FREE_INSUFFICIENT_QUOTA)
          }
        }
        break
      default:
        throwError(StatusCodes.INVALID_MEMBERSHIP_TYPE)
    }
  }

  /**
   * 检查会员状态
   * @param userId 用户ID
   * @param required 需要的配额数量
   * @throws Error 当检查不通过时抛出对应错误
   */
  public async checkMembershipByUserId(userId: string, required?: number) {
    const membership = await prisma.userMembership.findUnique({
      where: { userId },
      include: {
        enterprise: true,
      },
    })

    assert(membership, ERROR_MESSAGES.MEMBERSHIP_NOT_FOUND)
    assert(membership.status !== MemberStatus.SUSPENDED, ERROR_MESSAGES.MEMBERSHIP_SUSPENDED)
    assert(membership.status !== MemberStatus.DEVICE_BAN, ERROR_MESSAGES.MEMBERSHIP_DEVICE_BAN)

    const now = dayjs.utc().toDate()

    assert(
      membership.effectiveAt && membership.effectiveAt <= now,
      new Error(ERROR_MESSAGES.MEMBERSHIP_NOT_EFFECTIVE),
    )
    assert(
      membership.expireAt && membership.expireAt >= now,
      new Error(ERROR_MESSAGES.MEMBERSHIP_EXPIRED),
    )

    let remaining = 0
    switch (membership.type) {
      case MemberType.ENTERPRISE:
        assert(membership.enterprise, ERROR_MESSAGES.ENTERPRISE_NOT_FOUND)
        assert(
          membership.enterprise.status !== EnterpriseStatus.SUSPENDED,
          ERROR_MESSAGES.ENTERPRISE_SUSPENDED,
        )
        assert(
          membership.enterprise.status !== EnterpriseStatus.EXPIRED,
          new Error(ERROR_MESSAGES.ENTERPRISE_EXPIRED),
        )
        assert(
          membership.enterprise.effectiveAt && membership.enterprise.effectiveAt <= now,
          new Error(ERROR_MESSAGES.ENTERPRISE_NOT_EFFECTIVE),
        )
        assert(
          membership.enterprise.expireAt && membership.enterprise.expireAt >= now,
          new Error(ERROR_MESSAGES.ENTERPRISE_EXPIRED),
        )

        if (required) {
          const enterpriseRemaining =
            membership.enterprise.accountQuota - membership.enterprise.usedQuota
          assert(enterpriseRemaining > 0, ERROR_MESSAGES.ENTERPRISE_QUOTA_EXCEEDED)
          assert(enterpriseRemaining >= required, ERROR_MESSAGES.ENTERPRISE_INSUFFICIENT_QUOTA)
        }
        break
      case MemberType.PAID:
        remaining = membership.accountQuota - membership.usedQuota
        if (required) {
          assert(remaining > 0, ERROR_MESSAGES.PAID_QUOTA_EXCEEDED)
          assert(remaining >= required, ERROR_MESSAGES.PAID_INSUFFICIENT_QUOTA)
        }
        break
      case MemberType.FREE:
        remaining = membership.accountQuota - membership.usedQuota
        if (required) {
          assert(remaining > 0, ERROR_MESSAGES.FREE_QUOTA_EXCEEDED)
          assert(remaining >= required, ERROR_MESSAGES.FREE_INSUFFICIENT_QUOTA)
        }
        break
      default:
        throw new Error('Invalid membership type')
    }
  }

  // 用于任务失败或者查询为空时返还配额
  async addQuota(
    userId: string,
    type: QuotaType,
    amount: number,
    description?: string,
    taskId?: string,
    projectId?: string,
    createdBy: string = 'system',
  ) {
    const maxRetries = 3
    let attempt = 0

    while (attempt < maxRetries) {
      try {
        return await prisma.$transaction(
          async (tx) => {
            const latestMembership = await tx.userMembership.findUnique({
              where: { userId },
              include: { enterprise: true },
            })

            assert(latestMembership, ERROR_MESSAGES.MEMBERSHIP_NOT_FOUND)

            this.checkMembershipStatus(latestMembership)

            const isEnterpriseUser =
              latestMembership.type === MemberType.ENTERPRISE &&
              latestMembership.enterpriseId &&
              latestMembership.enterprise

            if (
              isEnterpriseUser &&
              latestMembership.enterprise?.usedQuota &&
              latestMembership.enterprise.usedQuota > 0
            ) {
              const newEnterpriseQuota = Math.max(
                0,
                (latestMembership.enterprise.usedQuota || 0) - amount,
              )

              if (newEnterpriseQuota !== latestMembership.enterprise.usedQuota) {
                await tx.enterprise.update({
                  where: { id: latestMembership.enterpriseId! },
                  data: {
                    usedQuota: newEnterpriseQuota,
                  },
                })
              }
            }

            let updated = latestMembership
            if (latestMembership.usedQuota && latestMembership.usedQuota > 0) {
              const newPersonalQuota = Math.max(0, latestMembership.usedQuota - amount)
              const newDailyUsage = Math.max(0, latestMembership.dailyUsage - amount)
              // 当已经使用的配额和日使用配额为0的时候或者一致的时候，不更新
              if (
                newPersonalQuota !== latestMembership.usedQuota ||
                newDailyUsage !== latestMembership.dailyUsage
              ) {
                updated = await tx.userMembership.update({
                  where: { id: latestMembership.id },
                  data: {
                    usedQuota: newPersonalQuota,
                    dailyUsage: newDailyUsage,
                  },
                  include: {
                    enterprise: true,
                  },
                })
              }
            }

            await tx.quotaLog.create({
              data: {
                userId,
                membershipId: latestMembership.id,
                usage: amount,
                type,
                taskId,
                projectId,
                description:
                  description ||
                  `Refunded quota: ${amount},update membership dailyUsage: ${latestMembership.dailyUsage} to ${latestMembership.dailyUsage - amount}`,
                enterpriseId: isEnterpriseUser ? latestMembership.enterpriseId! : undefined,
                metadata: {
                  quotaType: type,
                  cost: amount,
                  isEnterpriseRefund: isEnterpriseUser,
                  skipEnterpriseUpdate:
                    isEnterpriseUser &&
                    (latestMembership.enterprise?.usedQuota === 0 ||
                      latestMembership.enterprise?.usedQuota ===
                        Math.max(0, (latestMembership.enterprise?.usedQuota || 0) - amount)),
                  skipPersonalUpdate:
                    latestMembership.usedQuota === 0 ||
                    latestMembership.usedQuota === Math.max(0, latestMembership.usedQuota - amount),
                  originalEnterpriseQuota: isEnterpriseUser
                    ? latestMembership.enterprise?.usedQuota
                    : undefined,
                  originalPersonalQuota: latestMembership.usedQuota,
                },
                createdBy,
              },
            })
            return updated
          },
          {
            timeout: 15_000,
            isolationLevel: 'Serializable',
          },
        )
      } catch (error) {
        if ((error as any).code === 'P2034') {
          attempt++
          if (attempt >= maxRetries) {
            console.error('Max retries reached. Quota operation failed:', error)
            throw error
          }
          console.warn(`Retrying transaction due to conflict (attempt ${attempt})`)
        } else {
          console.error('Quota operation failed:', {
            error,
            userId,
            type,
            amount,
          })
          Sentry.captureException(error)
          throw error
        }
      }
    }
  }

  async deductQuota({
    userId,
    type,
    projectId,
    taskId,
    createdBy,
    count,
    metadata,
    responseDataRecord,
  }: DeductQuotaParams) {
    const maxRetries = 3
    let attempt = 0

    while (attempt < maxRetries) {
      try {
        let usage: number
        if (type === QuotaType.EASYKOL_DATA_TRACK_URL) {
          usage = QuotaCost.EASYKOL_DATA_TRACK_URL * (count || 1)
        } else if (type === QuotaType.EXCLUDE_LIST) {
          usage = Math.max(
            MIN_COST_FOR_EXCLUDE_LINK,
            PRICE_FOR_EXCLUDE_LINK * (Math.ceil((count || 10) / 5.0) * 5),
          )
        } else if (type === QuotaType.TT_WEB_LIST || type === QuotaType.INS_WEB_LIST) {
          usage = count || QuotaCost.TT_WEB_LIST || QuotaCost.INS_WEB_LIST
        } else if (quotaCostMap[type] !== undefined) {
          usage = quotaCostMap[type] as number
        } else {
          throw new Error(`Invalid quota deduction type: ${type}`)
        }

        return await prisma.$transaction(
          async (tx) => {
            const latestMembership = await tx.userMembership.findUniqueOrThrow({
              where: { userId },
              include: { enterprise: true },
            })

            const [isCardQueryAndCardSubscription, hasVisited] = await Promise.all([
              type === QuotaType.CARD_QUERY
                ? this.checkMemberCardSubscription(latestMembership)
                : false,
              type === QuotaType.CARD_QUERY &&
              responseDataRecord?.platform &&
              responseDataRecord?.platformAccount
                ? hasVisitedInLastWeek(
                    userId,
                    responseDataRecord.platform,
                    responseDataRecord.platformAccount,
                  )
                : false,
            ])

            if (hasVisited) {
              usage = QuotaCost.CARD_QUERY_HAS_VISITED_IN_LAST_WEEK
            } else if (isCardQueryAndCardSubscription) {
              usage = QuotaCost.CARD_QUERY_HAS_SUBSCRIPTION
            }

            // check the end quota
            if (usage > 0) {
              this.checkMembershipStatus(latestMembership, usage)
            }

            const isEnterpriseUser =
              latestMembership.type === MemberType.ENTERPRISE &&
              latestMembership.enterpriseId &&
              latestMembership.enterprise

            const updatePromises: Promise<any>[] = []

            // update enterprise quota
            if (isEnterpriseUser && usage > 0) {
              updatePromises.push(
                tx.enterprise.update({
                  where: { id: latestMembership.enterpriseId! },
                  data: {
                    usedQuota: { increment: usage },
                  },
                }),
              )
            }

            // update user quota
            if (usage > 0) {
              updatePromises.push(
                tx.userMembership.update({
                  where: { id: latestMembership.id },
                  data: {
                    usedQuota: { increment: usage },
                    dailyUsage: { increment: usage },
                  },
                  include: {
                    enterprise: true,
                  },
                }),
              )
            } else {
              updatePromises.push(Promise.resolve(latestMembership))
            }

            const results = await Promise.all(updatePromises)
            const updated = results.length > 1 ? results[1] : results[0]

            let description = `Used quota for ${type}: ${usage}`
            if (type === QuotaType.CARD_QUERY) {
              description += `, platform: ${metadata?.requestParams?.query?.platform}, platformAccount: ${metadata?.requestParams?.query?.handler || metadata?.requestParams?.query?.id}`
            } else if (
              type === QuotaType.AUDIENCE_ANALYSIS &&
              responseDataRecord?.platform &&
              responseDataRecord?.accountId
            ) {
              description += `, platform: ${responseDataRecord.platform}, accountId: ${responseDataRecord.accountId}`
            }

            await tx.quotaLog.create({
              data: {
                userId,
                membershipId: latestMembership.id,
                projectId,
                taskId,
                kolId: responseDataRecord?.kolId || '',
                usage: -usage,
                type,
                description,
                createdBy: createdBy || 'system',
                enterpriseId: isEnterpriseUser ? latestMembership.enterpriseId! : undefined,
                metadata: {
                  quotaType: type,
                  cost: usage,
                  isEnterpriseUsage: isEnterpriseUser,
                  isCardQueryAndCardSubscription,
                  originalEnterpriseQuota: isEnterpriseUser
                    ? latestMembership.enterprise?.usedQuota
                    : undefined,
                  originalPersonalQuota: latestMembership.usedQuota,
                  ...metadata,
                },
              },
            })

            return updated
          },
          {
            maxWait: 15_000,
            timeout: 15_000,
            isolationLevel: 'Serializable',
          },
        )
      } catch (error) {
        // 错误处理逻辑保持不变
        if ((error as any).code === 'P2034') {
          attempt++
          if (attempt >= maxRetries) {
            console.error('Max retries reached. Quota deduction failed:', error)
            throw error
          }
          console.warn(`Retrying transaction due to conflict (attempt ${attempt})`)
        } else {
          console.error('Quota deduction failed:', {
            error,
            ...{ userId, type, projectId, taskId, createdBy },
          })
          Sentry.captureException(error)
          throw error
        }
      }
    }
  }

  async deductDynamicQuota(deductDynamicQuotaParams: DeductDynamicQuotaParams) {
    const maxRetries = 3
    let attempt = 0
    const { userId, quotaType, quota, projectId, taskId, metadata } = deductDynamicQuotaParams
    while (attempt < maxRetries) {
      try {
        return await prisma.$transaction(
          async (tx) => {
            const latestMembership = await tx.userMembership.findUnique({
              where: { userId },
              include: { enterprise: true },
            })

            if (!latestMembership) {
              throw new Error(ERROR_MESSAGES.MEMBERSHIP_NOT_FOUND)
            }

            this.checkMembershipStatus(latestMembership, quota)

            const isEnterpriseUser =
              latestMembership.type === MemberType.ENTERPRISE &&
              latestMembership.enterpriseId &&
              latestMembership.enterprise

            const updatePromises: Promise<any>[] = []

            if (isEnterpriseUser && quota > 0) {
              updatePromises.push(
                tx.enterprise.update({
                  where: { id: latestMembership.enterpriseId! },
                  data: {
                    usedQuota: { increment: quota },
                  },
                }),
              )
            }

            if (quota > 0) {
              updatePromises.push(
                tx.userMembership.update({
                  where: { id: latestMembership.id },
                  data: {
                    usedQuota: { increment: quota },
                    dailyUsage: { increment: quota },
                  },
                  include: {
                    enterprise: true,
                  },
                }),
              )
            } else {
              updatePromises.push(Promise.resolve(latestMembership))
            }

            const results = await Promise.all(updatePromises)
            const updated = results.length > 1 ? results[1] : results[0]

            //  简化描述生成逻辑
            const description = `Used quota for ${quotaType}: ${quota}`

            await tx.quotaLog.create({
              data: {
                userId,
                membershipId: latestMembership.id,
                projectId,
                taskId,
                kolId: '',
                usage: -quota,
                type: quotaType,
                description,
                createdBy: userId || 'system',
                enterpriseId: isEnterpriseUser ? latestMembership.enterpriseId! : undefined,
                metadata: {
                  quotaType: quotaType,
                  cost: quota,
                  isEnterpriseUsage: isEnterpriseUser,
                  originalEnterpriseQuota: isEnterpriseUser
                    ? latestMembership.enterprise?.usedQuota
                    : undefined,
                  originalPersonalQuota: latestMembership.usedQuota,
                  ...metadata,
                },
              },
            })

            return updated
          },
          {
            timeout: 15_000,
            isolationLevel: 'Serializable',
          },
        )
      } catch (error) {
        // 错误处理逻辑保持不变
        if ((error as any).code === 'P2034') {
          attempt++
          if (attempt >= maxRetries) {
            console.error('Max retries reached. Quota deduction failed:', error)
            throw error
          }
          console.warn(`Retrying transaction due to conflict (attempt ${attempt})`)
        } else {
          console.error('Quota deduction failed:', {
            error,
            ...{ userId, quotaType, projectId, taskId },
          })
          Sentry.captureException(error)
          throw error
        }
      }
    }
  }

  // 更新会员信息
  async updateMembership(
    email: string,
    params: UpdateMembershipParams,
    description?: string,
    operatorId: string = 'system',
  ) {
    const user = await prisma.userInfo.findFirst({
      where: { email },
    })

    if (!user) {
      throw new Error(ERROR_MESSAGES.USER_NOT_FOUND)
    }

    const membership = await prisma.userMembership.findUnique({
      where: { userId: user?.userId },
    })

    if (!membership) {
      throw new Error(ERROR_MESSAGES.MEMBERSHIP_NOT_FOUND)
    }

    const userTimezone = params.timezone || membership.timezone || 'Asia/Shanghai'
    const updateData: any = {}

    // 记录原始数据，用于日志
    const previousData = { ...membership }

    // ，改接口是更新用户，需要取消掉企业的id
    updateData.enterpriseId = null

    // 只更新提供的字段
    if (params.type !== undefined) updateData.type = params.type
    if (params.status !== undefined) updateData.status = params.status
    if (params.timezone) updateData.timezone = params.timezone
    if (params.usedQuota !== undefined) updateData.usedQuota = params.usedQuota

    // 处理时间相关字段，需要时区转换
    if (params.effectiveAt) {
      updateData.effectiveAt = TimezoneService.convertToUserTimezone(
        params.effectiveAt,
        userTimezone,
      )
    }

    if (params.expireAt) {
      updateData.expireAt = TimezoneService.convertToUserTimezone(params.expireAt, userTimezone)
    }

    // 处理配额变更
    let quotaChange = 0
    if (typeof params.accountQuota === 'number') {
      updateData.accountQuota = params.accountQuota
      quotaChange = params.accountQuota - membership.accountQuota
    }
    // 用户恢复正常状态
    params.status = MemberStatus.ACTIVE
    updateData.status = MemberStatus.ACTIVE
    // 事务处理更新和日志
    try {
      const updatedMembership = await prisma.$transaction(
        async (tx) => {
          const updated = await tx.userMembership.update({
            where: { id: membership.id },
            data: updateData,
          })

          await tx.quotaLog.create({
            data: {
              userId: user.userId,
              membershipId: membership.id,
              usage: quotaChange,
              type: QuotaType.ADMIN,
              description: description || this.generateUpdateDescription(params, previousData),
              createdBy: operatorId,
              metadata: {
                timezone: userTimezone,
                previousData,
                updateData,
                changes: this.getChangedFields(previousData, updateData),
              },
            },
          })

          return updated
        },
        { timeout: 15000 },
      )

      return updatedMembership
    } catch (error) {
      console.error('Update membership failed:', error)
      Sentry.captureException(error)
      throw new Error(
        ERROR_MESSAGES.UPDATE_FAILED(error instanceof Error ? error.message : 'Unknown error'),
      )
    }
  }

  // 生成更新描述
  private generateUpdateDescription(params: UpdateMembershipParams, previousData: any): string {
    const changes: string[] = []

    if (params.type && params.type !== previousData.type) {
      changes.push(`Member type changed from ${previousData.type} to ${params.type}`)
    }
    if (params.status && params.status !== previousData.status) {
      changes.push(`Status changed from ${previousData.status} to ${params.status}`)
    }
    if (
      typeof params.accountQuota === 'number' &&
      params.accountQuota !== previousData.accountQuota
    ) {
      changes.push(`Quota changed from ${previousData.accountQuota} to ${params.accountQuota}`)
    }
    if (params.timezone && params.timezone !== previousData.timezone) {
      changes.push(`Timezone changed from ${previousData.timezone} to ${params.timezone}`)
    }
    if (params.expireAt) {
      changes.push(`Expiration date updated to ${params.expireAt.toISOString()}`)
    }
    if (typeof params.usedQuota === 'number' && params.usedQuota !== previousData.usedQuota) {
      changes.push(`Used quota changed from ${previousData.usedQuota} to ${params.usedQuota}`)
    }

    return changes.length > 0
      ? `Admin update: ${changes.join(', ')}`
      : 'Admin updated membership information'
  }

  private getChangedFields(previousData: any, updateData: any): Record<string, any> {
    const changes: Record<string, any> = {}

    Object.keys(updateData).forEach((key) => {
      if (previousData[key] !== updateData[key]) {
        changes[key] = {
          from: previousData[key],
          to: updateData[key],
        }
      }
    })

    return changes
  }

  /**
   * 检查用户账号状态,企业账号状态,有效期以及过期状态,配额状态
   * @param userId 用户ID
   * @param required 需要的配额数量
   * @returns 配额状态和错误信息
   */
  async checkMemberShipQuotaStatus(
    userId: string,
    required: number,
  ): Promise<{
    hasQuota: boolean
    error?: ApiResponse<null>
  }> {
    const membership = await this.getUserQuota(userId)
    if (!membership) {
      return {
        hasQuota: false,
        error: errorResponse(StatusCodes.MEMBERSHIP_NOT_FOUND, 'MEMBERSHIP_NOT_FOUND'),
      }
    }

    if (membership.status === MemberStatus.SUSPENDED) {
      return {
        hasQuota: false,
        error: errorResponse(StatusCodes.MEMBERSHIP_SUSPENDED, 'MEMBERSHIP_SUSPENDED'),
      }
    }

    if (membership?.status === MemberStatus.DEVICE_BAN) {
      return {
        hasQuota: false,
        error: errorResponse(StatusCodes.DEVICE_BAN, 'DEVICE_BAN'),
      }
    }

    const now = dayjs.utc().toDate()

    // 检查会员有效期
    if (membership.effectiveAt && membership.effectiveAt > now) {
      return {
        hasQuota: false,
        error: errorResponse(
          StatusCodes.MEMBERSHIP_NOT_EFFECTIVE,
          'MEMBERSHIP_NOT_EFFECTIVE',
          `Membership will be effective from ${dayjs(membership.effectiveAt).format('YYYY-MM-DD HH:mm:ss')}`,
        ),
      }
    }

    if (membership.expireAt && membership.expireAt < now) {
      return {
        hasQuota: false,
        error: errorResponse(
          StatusCodes.MEMBERSHIP_EXPIRED,
          'MEMBERSHIP_EXPIRED',
          `Membership expired at ${dayjs(membership.expireAt).format('YYYY-MM-DD HH:mm:ss')}`,
        ),
      }
    }

    const remaining = membership.accountQuota - membership.usedQuota
    switch (membership.type) {
      case MemberType.FREE:
        if (remaining <= 0) {
          return {
            hasQuota: false,
            error: errorResponse(StatusCodes.FREE_QUOTA_EXCEEDED, 'FREE_QUOTA_EXCEEDED'),
          }
        }
        if (remaining < required) {
          return {
            hasQuota: false,
            error: errorResponse(
              StatusCodes.FREE_INSUFFICIENT_QUOTA,
              'FREE_INSUFFICIENT_QUOTA',
              `Insufficient free quota.`,
            ),
          }
        }
        break

      case MemberType.PAID:
        if (remaining <= 0) {
          return {
            hasQuota: false,
            error: errorResponse(StatusCodes.PAID_QUOTA_EXCEEDED, 'PAID_QUOTA_EXCEEDED'),
          }
        }
        if (remaining < required) {
          return {
            hasQuota: false,
            error: errorResponse(
              StatusCodes.PAID_INSUFFICIENT_QUOTA,
              'PAID_INSUFFICIENT_QUOTA',
              `Insufficient member quota,please upgrade your membership or contact support.`,
            ),
          }
        }
        break

      case MemberType.ENTERPRISE:
        // 检查企业账户是否存在
        if (!membership.enterprise) {
          return {
            hasQuota: false,
            error: errorResponse(StatusCodes.ENTERPRISE_NOT_FOUND, 'ENTERPRISE_NOT_FOUND'),
          }
        }
        // 检查企业账户状态
        if (membership.enterprise.status === EnterpriseStatus.SUSPENDED) {
          return {
            hasQuota: false,
            error: errorResponse(StatusCodes.ENTERPRISE_SUSPENDED, 'ENTERPRISE_SUSPENDED'),
          }
        }

        // 检查企业账户有效期
        if (membership.enterprise.effectiveAt && membership.enterprise.effectiveAt > now) {
          return {
            hasQuota: false,
            error: errorResponse(
              StatusCodes.ENTERPRISE_NOT_EFFECTIVE,
              'ENTERPRISE_NOT_EFFECTIVE',
              `Enterprise membership will be effective from ${dayjs(membership.enterprise.effectiveAt).format('YYYY-MM-DD HH:mm:ss')}`,
            ),
          }
        }

        // 过期时间
        if (
          (membership.enterprise.expireAt && membership.enterprise.expireAt < now) ||
          membership.enterprise.status === EnterpriseStatus.EXPIRED
        ) {
          return {
            hasQuota: false,
            error: errorResponse(
              StatusCodes.ENTERPRISE_EXPIRED,
              'ENTERPRISE_EXPIRED',
              `Enterprise membership expired at ${dayjs(membership.enterprise.expireAt).format('YYYY-MM-DD HH:mm:ss')}`,
            ),
          }
        }

        // 检查企业账户总体余额
        const enterpriseRemaining =
          membership.enterprise.accountQuota - membership.enterprise.usedQuota
        if (enterpriseRemaining <= 0) {
          return {
            hasQuota: false,
            error: errorResponse(
              StatusCodes.ENTERPRISE_QUOTA_EXCEEDED,
              'ENTERPRISE_QUOTA_EXCEEDED',
            ),
          }
        }

        // 检查是否有足够
        if (enterpriseRemaining < required) {
          return {
            hasQuota: false,
            error: errorResponse(
              StatusCodes.ENTERPRISE_INSUFFICIENT_QUOTA,
              'ENTERPRISE_INSUFFICIENT_QUOTA',
              `Insufficient enterprise quota.`,
            ),
          }
        }
        // 个人使用限制优先级大于企业成员每日限制
        if (membership.enableEnterpriseQuotaDailyLimit) {
          // 检查企业成员每日使用额度
          if (
            membership.enterpriseQuotaDailyLimit !== -1 &&
            membership.dailyUsage + required > membership.enterpriseQuotaDailyLimit
          ) {
            return {
              hasQuota: false,
              error: errorResponse(
                StatusCodes.ENTERPRISE_PERSONAL_DAILY_USAGE_LIMIT_EXCEEDED,
                'ENTERPRISE_PERSONAL_DAILY_USAGE_LIMIT_EXCEEDED',
                'enterprise member personal daily usage limit exceeded',
              ),
            }
          }
        } else {
          //检查企业成员每日使用额度, 检查每日使用量
          // -1 表示无限制，跳过检查
          if (
            membership.enterprise?.memberUsageDailyLimit !== -1 &&
            membership.dailyUsage + required > membership.enterprise?.memberUsageDailyLimit
          ) {
            return {
              hasQuota: false,
              error: errorResponse(
                StatusCodes.ENTERPRISE_DAILY_USAGE_LIMIT_EXCEEDED,
                'ENTERPRISE_DAILY_USAGE_LIMIT_EXCEEDED',
                'enterprise member daily usage limit exceeded',
              ),
            }
          }
        }
        break
    }

    return { hasQuota: true }
  }

  // 获取配额使用记录
  async getQuotaLogs(
    userId: string,
    options?: {
      startDate?: Date
      endDate?: Date
      type?: QuotaType
      limit?: number
    },
  ): Promise<QuotaLog[]> {
    const membership = await prisma.userMembership.findUnique({
      where: { userId },
    })

    if (!membership) return []

    return await prisma.quotaLog.findMany({
      where: {
        userId,
        createdAt: {
          gte: options?.startDate,
          lte: options?.endDate,
        },
        type: options?.type,
      },
      orderBy: { createdAt: 'desc' },
      take: options?.limit || 50,
    })
  }

  async updateTimezone(userId: string, timezone: string) {
    // 验证时区格式：州/城市
    if (!/^[A-Z][a-z]+\/[A-Za-z_]+$/.test(timezone)) {
      throw new Error('Invalid timezone format. Must be in format: State/City')
    }

    try {
      new Date().toLocaleString('en-US', { timeZone: timezone })
    } catch (e) {
      throw new Error('Invalid timezone. Please provide a valid timezone')
    }

    const currentMembership = await prisma.userMembership.findUnique({
      where: { userId },
      select: {
        effectiveAt: true,
        expireAt: true,
        timezone: true,
      },
    })

    if (!currentMembership) {
      throw new Error(ERROR_MESSAGES.MEMBERSHIP_NOT_FOUND)
    }
    if (currentMembership.timezone === timezone) {
      return currentMembership
    }

    const newEffectiveAt = TimezoneService.convertBetweenTimezones(
      currentMembership.effectiveAt,
      currentMembership.timezone || 'Asia/Shanghai',
      timezone,
    )
    const newExpireAt = TimezoneService.convertBetweenTimezones(
      currentMembership.expireAt,
      currentMembership.timezone || 'Asia/Shanghai',
      timezone,
    )

    return await prisma.userMembership.update({
      where: { userId },
      data: {
        timezone,
        effectiveAt: newEffectiveAt,
        expireAt: newExpireAt,
      },
      select: {
        userId: true,
        timezone: true,
        effectiveAt: true,
        expireAt: true,
        updatedAt: true,
      },
    })
  }

  async getEmails(): Promise<string[]> {
    const users = await prisma.userInfo.findMany({
      select: { email: true },
      orderBy: {
        createdAt: 'desc',
      },
    })
    const validEmails = users.map((user) => user.email).filter((email) => email !== null)
    return validEmails
  }

  async getMemberType(userId: string): Promise<string> {
    const membership = await prisma.userMembership.findUnique({
      where: { userId },
    })
    if (!membership || membership.status === MemberStatus.SUSPENDED) {
      throw new Error(ERROR_MESSAGES.MEMBERSHIP_SUSPENDED)
    }
    return membership?.type
  }

  async getMembershipUsers(type: MemberType): Promise<MemberShipInfo[]> {
    const users = await prisma.userMembership.findMany({
      where: {
        type,
        status: MemberStatus.ACTIVE,
        user: {
          NOT: {
            email: null,
          },
        },
      },
      include: {
        user: {
          select: {
            email: true,
          },
        },
      },
      orderBy: {
        updatedAt: 'desc',
      },
    })

    return users.map((membership) => ({
      userId: membership.userId,
      email: membership.user?.email || '',
      effectiveAt: membership.effectiveAt,
      expireAt: membership.expireAt,
      accountQuota: membership.accountQuota,
      usedQuota: membership.usedQuota,
      createdAt: membership.createdAt,
    }))
  }

  async getDailyQuota(email: string, dateFilter: DateFilter = {}): Promise<DailyQuotaResult[]> {
    const { gte, lte } = dateFilter

    const user = await prisma.userInfo.findFirst({
      where: { email },
    })

    if (!user) {
      return []
    }

    const quotaLogs = await prisma.quotaLog.findMany({
      where: {
        userId: user.userId,
        type: {
          notIn: [QuotaType.RESET, QuotaType.ADMIN, QuotaType.EXPIRE],
        },
        createdAt: {
          ...(gte && { gte: new Date(gte) }),
          ...(lte && { lte: new Date(lte) }),
        },
        usage: { lt: 0 }, // 只统计消耗的配额
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    const dailyUsage = quotaLogs.reduce((acc: Map<string, number>, log) => {
      const date = dayjs(log.createdAt).tz('Asia/Shanghai').format('YYYY-MM-DD')
      const usage = Math.abs(log.usage)
      acc.set(date, (acc.get(date) || 0) + usage)
      return acc
    }, new Map())

    return Array.from(dailyUsage.entries())
      .map(([date, usage]) => ({
        date: new Date(date),
        userId: user.userId,
        email: email,
        daily_usage: usage,
      }))
      .sort((a, b) => b.date.getTime() - a.date.getTime())
  }

  async getQuotaDetails(email: string, dateFilter: DateFilter = {}): Promise<QuotaDetailResult[]> {
    const { gte, lte } = dateFilter

    const user = await prisma.userInfo.findFirst({
      where: { email },
    })

    if (!user) {
      return []
    }

    const quotaLogs = await prisma.quotaLog.findMany({
      where: {
        userId: user.userId,
        createdAt: {
          ...(gte && { gte: new Date(gte) }),
          ...(lte && { lte: new Date(lte) }),
        },
        usage: { not: 0 },
      },
      orderBy: {
        createdAt: 'desc',
      },
      select: {
        createdAt: true,
        usage: true,
        type: true,
        description: true,
      },
    })

    return quotaLogs.map((log) => ({
      time: log.createdAt,
      quota_cost: Math.abs(log.usage),
      quota_type: log.type,
      description: log.description,
      email: email,
      userId: user.userId,
    }))
  }

  async getEnterpriseInfo(userId: string): Promise<MemberShipEnterpriseInfo> {
    const membership = await prisma.userMembership.findUnique({
      where: { userId },
      include: {
        enterprise: true,
        user: {
          select: {
            email: true,
          },
        },
      },
    })

    if (!membership || !membership?.enterprise) {
      return {
        hasEnterprise: false,
        enterpriseId: '',
        isEnterpriseAdmin: false,
        enterpriseName: '',
        enterpriseStatus: EnterpriseStatus.SUSPENDED,
        email: membership?.user?.email || '',
      }
    }

    return {
      hasEnterprise: true,
      enterpriseId: membership.enterprise.id,
      isEnterpriseAdmin: membership.isEnterpriseAdmin,
      enterpriseName: membership.enterprise.name,
      enterpriseStatus: membership.enterprise.status,
      email: membership.user?.email || '',
    }
  }

  async checkMemberCardSubscription(membership: UserMembership): Promise<boolean> {
    const now = dayjs.utc()
    if (
      membership.cardSubscriptionStatus === CardSubscriptionStatus.ACTIVE &&
      membership.cardSubscriptionExpireAt &&
      dayjs.utc(membership.cardSubscriptionExpireAt).isBefore(now)
    ) {
      // 顺便更新状态为过期
      await prisma.userMembership.update({
        where: { id: membership.id },
        data: {
          cardSubscriptionStatus: CardSubscriptionStatus.EXPIRED,
        },
      })
      return false
    }

    return !!(
      membership.cardSubscriptionStatus === CardSubscriptionStatus.ACTIVE &&
      membership.cardSubscriptionEffectiveAt &&
      membership.cardSubscriptionExpireAt &&
      dayjs.utc(membership.cardSubscriptionEffectiveAt).isBefore(now) &&
      dayjs.utc(membership.cardSubscriptionExpireAt).isAfter(now)
    )
  }

  /**
   * 刷新用户会员信息，包括账号状态、配额和会员信息卡状态
   * @param userId 用户ID
   * @returns 更新后的完整会员信息
   */
  async refreshUserMembershipInfo(userId: string): Promise<UserMembership> {
    // updated membership status and quota
    const membership = await this.refreshUserStatusInPayMent(userId)
    if (!membership) {
      throw new Error(ERROR_MESSAGES.MEMBERSHIP_NOT_FOUND)
    }

    const now = dayjs.utc()

    // check member card subscription status
    if (
      membership.cardSubscriptionStatus === CardSubscriptionStatus.ACTIVE &&
      membership.cardSubscriptionExpireAt &&
      membership.cardSubscriptionEffectiveAt &&
      dayjs(membership.cardSubscriptionExpireAt).isAfter(dayjs(now)) &&
      dayjs(membership.cardSubscriptionEffectiveAt).isBefore(dayjs(now))
    ) {
      return membership
    } else {
      return await prisma.userMembership.update({
        where: { id: membership.id },
        data: {
          cardSubscriptionStatus: CardSubscriptionStatus.EXPIRED,
        },
      })
    }
  }

  /**
   * 刷新用户会员信息，包括账号状态、配额和会员信息卡状态
   * @param userId 用户ID
   * @returns 更新后的完整会员信息
   */
  async refreshEnterpriseMembershipInfoCardStatus(membershipId: string): Promise<UserMembership> {
    // updated membership status and quota
    const membership = await prisma.userMembership.findUniqueOrThrow({
      where: { id: membershipId },
    })

    const now = dayjs.utc()

    // check member card subscription status
    if (
      membership.cardSubscriptionStatus === CardSubscriptionStatus.ACTIVE &&
      membership.cardSubscriptionExpireAt &&
      membership.cardSubscriptionEffectiveAt &&
      dayjs(membership.cardSubscriptionExpireAt).isAfter(dayjs(now)) &&
      dayjs(membership.cardSubscriptionEffectiveAt).isBefore(dayjs(now))
    ) {
      return membership
    } else {
      return await prisma.userMembership.update({
        where: { id: membership.id },
        data: {
          cardSubscriptionStatus: CardSubscriptionStatus.EXPIRED,
        },
      })
    }
  }

  async getCardSubscriptionUsers() {
    const users = await prisma.userMembership.findMany({
      where: {
        cardSubscriptionStatus: CardSubscriptionStatus.ACTIVE,
        user: {
          NOT: {
            email: null,
          },
        },
      },
      include: {
        user: {
          select: {
            email: true,
          },
        },
      },
      orderBy: {
        cardSubscriptionEffectiveAt: 'desc',
      },
    })

    return users.map((membership) => ({
      userId: membership.userId,
      email: membership.user?.email || '',
      cardSubscriptionEffectiveAt: membership.cardSubscriptionEffectiveAt,
      cardSubscriptionExpireAt: membership.cardSubscriptionExpireAt,
      cardSubscriptionStatus: membership.cardSubscriptionStatus,
      createdAt: membership.createdAt,
      updatedAt: membership.updatedAt,
    }))
  }

  // 通过邮箱订阅卡片包月
  async subscribeCard(email: string, duration: number = 1) {
    const user = await prisma.userInfo.findFirstOrThrow({
      where: { email },
    })

    const membership = await prisma.userMembership.findUniqueOrThrow({
      where: { userId: user.userId },
    })

    const now = new Date()
    let effectiveAt = now
    let expireAt = now

    const hasActiveSubscription =
      membership.cardSubscriptionStatus === CardSubscriptionStatus.ACTIVE
    const isSubscriptionValid =
      membership.cardSubscriptionExpireAt && new Date(membership.cardSubscriptionExpireAt) > now

    if (hasActiveSubscription && isSubscriptionValid) {
      // 如果用户订阅未过期，在过期时间上添加/减少时间
      expireAt = new Date(membership.cardSubscriptionExpireAt!)
      expireAt.setMonth(expireAt.getMonth() + duration)

      // 如果减少时间后过期时间早于当前时间，则将过期时间设为当前时间
      if (expireAt < now) {
        expireAt = now
      }
    } else {
      // 如果用户订阅已过期，从当前时间开始计算
      effectiveAt = now
      expireAt = new Date(now)

      // 只有正数才添加时间，负数不处理（已过期的账户不能再减少时间）
      if (duration > 0) {
        expireAt.setMonth(expireAt.getMonth() + duration)
      }
    }

    // 更新会员信息
    const updated = await prisma.userMembership.update({
      where: { userId: user.userId },
      data: {
        cardSubscriptionStatus: CardSubscriptionStatus.ACTIVE,
        cardSubscriptionEffectiveAt: effectiveAt,
        cardSubscriptionExpireAt: expireAt,
      },
      include: {
        user: {
          select: {
            email: true,
          },
        },
      },
    })

    return {
      userId: updated.userId,
      email: updated.user?.email,
      cardSubscriptionStatus: updated.cardSubscriptionStatus,
      cardSubscriptionEffectiveAt: updated.cardSubscriptionEffectiveAt,
      cardSubscriptionExpireAt: updated.cardSubscriptionExpireAt,
      updatedAt: updated.updatedAt,
    }
  }
}
