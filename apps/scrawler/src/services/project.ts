import { IgPost } from '@/api/@types/rapidapi/Instagram'
import { ChannelVideo } from '@/api/@types/rapidapi/Youtube'
import InstagramApi from '@/api/instagram'
import TiktokApi from '@/api/tiktok'
import YoutubeApi from '@/api/youtube'
import Sentry from '@/infras/sentry'
import { KolInfoWithScore, TtVideoBasicInfo } from '@/types/kol'
import { ProjectCandidate, ProjectCandidates } from '@/types/project'
import { BreakTaskSearchResponse } from '@/types/response/search'
import { PaginationService } from '@/utils/pagination'
import {
  KolPlatform,
  ProjectMembershipRole,
  TaskType,
  YouTubeChannel,
  prisma,
} from '@repo/database'
import { filterUnreadKolIds } from './project-kol'

export const createProjectWithUser = async (
  userId: string,
  productInfo: {
    title: string
    description: string
  },
) => {
  return await prisma.$transaction(async (prisma) => {
    console.log('Inside transaction')
    const project = await prisma.project.create({
      data: {
        ...productInfo,
      },
    })

    const membership = await prisma.projectMembership.create({
      data: {
        userId,
        projectId: project.id,
        role: ProjectMembershipRole.OWNER,
      },
    })
    console.log('Membership created:', membership)

    // product.membership = [membership];

    return project
  })
}

export const findProjectById = async (id: string) => {
  return await prisma.project.findFirst({
    where: {
      id,
      deletedAt: null,
    },
  })
}

export const isOwnerForProject = async (userId: string, projectId: string) => {
  return await prisma.projectMembership.findFirst({
    where: {
      userId,
      projectId,
      role: ProjectMembershipRole.OWNER,
    },
  })
}

export const hasAuthForProject = async (userId: string, projectId: string) => {
  if (!userId || !projectId) {
    return false
  }

  const res = await prisma.projectMembership.findFirst({
    where: {
      userId,
      projectId,
    },
  })
  return !!res
}

export const getAllAvailableProjectByUser = async (userId: string) => {
  // 先获取用户的所有项目ID
  const memberships = await prisma.projectMembership.findMany({
    where: {
      userId,
    },
    select: {
      projectId: true,
    },
  })

  const projectIds = memberships.map((m) => m.projectId)

  const projects = await prisma.project.findMany({
    where: {
      id: {
        in: projectIds,
      },
      deletedAt: null,
    },
  })

  if (projects.length === 0) {
    const newProject = await prisma.$transaction(
      async (prisma) => {
        const createdProject = await prisma.project.create({
          data: {
            title: 'Default Project',
            description: 'This is a default project.',
          },
        })

        await prisma.projectMembership.create({
          data: {
            userId: userId,
            projectId: createdProject.id,
            role: ProjectMembershipRole.OWNER,
          },
        })

        return createdProject
      },
      { timeout: 15_000 },
    )

    return [newProject]
  }

  return projects
}

export const terminateProject = async (id: string) => {
  const project = await prisma.project.findFirst({ where: { id, deletedAt: null } })
  if (!project) {
    throw new Error('project not found')
  }
  const tasks = await prisma.similarChannelTask.findFirst({
    where: {
      projectId: id,
    },
    orderBy: {
      createdAt: 'desc',
    },
  })

  if (!tasks) {
    throw new Error('project has no tasks')
  }
  if (tasks.isTerminated) {
    return
  }
  await prisma.similarChannelTask.update({
    where: {
      id: tasks.id,
    },
    data: {
      isTerminated: true,
    },
  })
}

/**
 * 根据任务类型终止项目相关任务并清理数据
 * @param id - 项目ID
 * @param taskType - 任务类型
 */
export const terminateProjectByTaskType = async (id: string, taskType: TaskType) => {
  const project = await prisma.project.findFirstOrThrow({
    where: {
      id,
      deletedAt: null,
    },
  })
  await Promise.all([
    cleanupProjectCandidate(id, taskType),
    terminateLatestTask(id, taskType),
    taskType === TaskType.SIMILAR && cleanupProjectConfig(project),
    taskType === TaskType.LONG_CRAWLER && completeTask(id, ),
  ])
}
/**
 * 清理项目配置
 */
async function cleanupProjectConfig(project: { id: string; config: any }) {
  if (!project.config) {
    return
  }

  await prisma.project.update({
    where: { id: project.id },
    data: { config: {} },
  })
}

/**
 * 清理项目候选数据
 */
async function cleanupProjectCandidate(projectId: string, taskType: TaskType) {
  const projectCandidate = await prisma.projectCandidate.findUnique({
    where: {
      projectId_type: {
        projectId,
        type: taskType,
      },
    },
  })

  if (projectCandidate) {
    if (taskType === TaskType.HASH_TAG_BREAK) {
      await prisma.projectCandidate.update({
        where: { id: projectCandidate.id },
        data: {
          candidates: {},
          meta: {},
        },
      })
    }
  }

  const project = await prisma.project.findUnique({
    where: { id: projectId },
    select: { candidates: true },
  })

  if (project?.candidates) {
    const currentCandidates = project.candidates as Record<string, any>

    if (currentCandidates[taskType]) {
      delete currentCandidates[taskType]

      await prisma.project.update({
        where: { id: projectId },
        data: {
          candidates: currentCandidates,
        },
      })
    }
  }
}

/**
 * 终止最新任务
 */
async function terminateLatestTask(projectId: string, taskType: TaskType) {
  const latestTask = await prisma.similarChannelTask.findFirst({
    where: {
      projectId,
      type: taskType,
    },
    orderBy: {
      createdAt: 'desc',
    },
  })

  if (!latestTask) {
    throw new Error('未找到指定类型的任务')
  }

  if (latestTask.isTerminated) {
    return
  }

  await prisma.similarChannelTask.update({
    where: { id: latestTask.id },
    data: { isTerminated: true },
  })
}

export const deleteProject = async (id: string, userId: string) => {
  // 项目owner才能删除
  const membership = await prisma.projectMembership.findFirst({
    where: {
      projectId: id,
      userId,
      role: ProjectMembershipRole.OWNER,
    },
  })
  if (!membership) {
    throw new Error('user is not owner of this project')
  }
  const project = await prisma.project.findFirst({
    where: {
      id,
      deletedAt: null,
    },
  })

  if (!project) {
    throw new Error('project not found or deleted')
  }

  // 找到创建时间最早的project
  const defaultProject = await prisma.project.findFirst({
    where: {
      deletedAt: null,
    },
    orderBy: {
      createdAt: 'asc',
    },
  })

  if (id === defaultProject?.id) {
    throw new Error('default project cannot be deleted')
  }

  return await prisma.project.update({
    where: { id },
    data: {
      deletedAt: new Date(),
    },
  })
}

export const resetProject = async (projectId: string) => {
  try {
    await prisma.$transaction(
      async (tx) => {
        // 1. 获取所有需要删除的 ProjectKol IDs
        const projectKolIds = await tx.projectKol.findMany({
          where: {
            projectId: projectId,
          },
          select: {
            id: true,
          },
        })

        // 2. 删除相关的 EmailRecord
        if (projectKolIds.length > 0) {
          await tx.emailRecord.deleteMany({
            where: {
              projectKolId: {
                in: projectKolIds.map((pk) => pk.id),
              },
            },
          })
        }

        // 3. 删除所有相关的 ProjectKol
        await tx.projectKol.deleteMany({
          where: {
            projectId: projectId,
          },
        })

        // 4. 删除所有相关的 TaskKol
        // await tx.taskKol.deleteMany({
        //   where: {
        //     projectId: projectId,
        //   },
        // })

        // 5. 删除所有相关的 SimilarChannelTask
        await tx.similarChannelTask.deleteMany({
          where: {
            projectId: projectId,
          },
        })
      },
      { timeout: 30_000 },
    )

    return true
  } catch (error) {
    console.error('Reset project failed:', error)
    Sentry.captureException(error)
    throw error
  }
}

export async function getProjectCandidates(
  projectId: string,
  taskType: TaskType,
  platform: KolPlatform,
) {
  const project = await prisma.project.findUniqueOrThrow({
    where: { id: projectId },
    omit: {
      candidates: false,
    },
  })

  const candidates = (project.candidates as unknown as ProjectCandidates)?.[taskType]
  if (!candidates || !candidates.kols || !candidates.kols.length) {
    // 如果候选列表为空，则返回空数组
    return []
  }

  const kolIds = await filterUnreadKolIds(
    project.id,
    candidates.kols.map((k) => k.kolId),
  )

  const unreadCandidates = candidates.kols
    .filter((k) => kolIds.includes(k.kolId))
    .sort((a, b) => {
      //TODO: remove this after score is fixed
      if ((a.score as any)?.score) {
        return (b.score as any)?.score - (a.score as any)?.score
      }
      return b.score - a.score
    })

  let query: any = {}
  query = {
    platformAccount: { in: unreadCandidates.map((c) => c.platformId) },
    platform,
  }

  const kols = await prisma.kolInfo.findMany({
    where: query,
  })

  const kolMap = kols.reduce((acc: any, kol: any) => {
    acc[kol.platformAccount] = kol
    return acc
  }, {})
  const result: KolInfoWithScore[] = unreadCandidates.map((candidate) => {
    return {
      ...kolMap[candidate.platformId],
      score: candidate.score,
    } as KolInfoWithScore
  })
  return result
}

async function handleHashTagBreakCase(
  projectId: string,
  platform: KolPlatform,
  options?: { page?: number; pageSize?: number },
) {
  console.log(`处理 HASH_TAG_BREAK 类型的任务查询，项目ID: ${projectId}`)
  // 从 ProjectCandidate 表中获取数据
  const projectCandidate = await prisma.projectCandidate.findUnique({
    where: {
      projectId_type: {
        projectId: projectId,
        type: TaskType.HASH_TAG_BREAK,
      },
    },
  })

  if (!projectCandidate || !projectCandidate.candidates) {
    console.log(`未找到项目 ${projectId} 的 HASH_TAG_BREAK 任务结果`)
    return {
      data: [],
      total: 0,
      page: options?.page || 1,
      pageSize: options?.pageSize || 100,
      totalPages: 0,
      hasMore: false,
      progress: {
        total: 0,
        current: 0,
        count: 0,
      },
    }
  }

  const { page, pageSize, skip } = PaginationService.handlePagination(options)
  const allKols = (projectCandidate.candidates as any)?.kols || []
  const total = allKols.length
  const totalPages = Math.ceil(total / pageSize)

  // slice分页取数据
  const candidatesData = allKols.slice(skip, skip + pageSize) || []
  if (candidatesData.length === 0) {
    const meta = (projectCandidate.meta as any) || {}
    return {
      data: [],
      total,
      page,
      pageSize,
      totalPages,
      hasMore: false,
      updatedAt: meta.updatedAt,
      progress: meta.progress,
    }
  }

  switch (platform as KolPlatform) {
    case KolPlatform.INSTAGRAM: {
      console.log('开始处理 Instagram 数据')
      const kols = await prisma.kolInfo.findMany({
        where: {
          platformAccount: { in: candidatesData.map((c: ProjectCandidate) => c.platformId) },
          platform: KolPlatform.INSTAGRAM,
        },
        include: {
          instagramUser: {
            omit: {
              posts: false,
            },
          },
          ProjectKol: {
            where: {
              projectId: projectId,
            },
            select: {
              attitude: true,
            },
          },
        },
      })
      console.log(`找到 ${kols.length} 个 Instagram 用户`)

      const kolPromises = kols.map(async (kol: any) => {
        if (kol.instagramUser) {
          let posts: IgPost[] = []
          if (kol.instagramUser.posts && Array.isArray(kol.instagramUser.posts)) {
            console.log(
              `用户 ${kol.instagramUser.username} 有 ${kol.instagramUser.posts.length} 个帖子`,
            )
            posts = kol.instagramUser.posts.map((post: any) => ({
              ...post,
              id: post.id ? String(post.id) : post.id,
              comment_count: post.comment_count ? Number(post.comment_count) : 0,
              like_count: post.like_count ? Number(post.like_count) : 0,
              play_count: post.play_count ? Number(post.play_count) : 0,
              created_at: post.created_at ? Number(post.created_at) : 0,
            }))
          } else {
            console.log(`用户 ${kol.instagramUser.username} 没有缓存的帖子数据，尝试从 API 获取`)
            const userPosts = await InstagramApi.getInstance().getPosts(kol.instagramUser.username)
            console.log(`从 API 获取到 ${userPosts?.length || 0} 个帖子`)
            posts =
              userPosts?.map((post: any) => ({
                ...post,
                id: post.id ? String(post.id) : post.id,
                comment_count: post.comment_count ? Number(post.comment_count) : 0,
                like_count: post.like_count ? Number(post.like_count) : 0,
                play_count: post.play_count ? Number(post.play_count) : 0,
                created_at: post.created_at ? Number(post.created_at) : 0,
              })) || []

            if (posts.length > 0) {
              console.log(`准备更新用户 ${kol.instagramUser.username} 的帖子数据到数据库`)
              try {
                await prisma.instagramUserInfo.update({
                  where: { username: kol.instagramUser.username },
                  data: {
                    posts: posts,
                    updatedAt: new Date(),
                  },
                })
                console.log('数据库更新成功')
              } catch (error) {
                console.error(`更新Instagram用户帖子数据失败: ${kol.instagramUser.username}`, error)
                Sentry.captureException(error)
              }
            }
          }

          console.log(`最终处理完成的帖子数量: ${posts.length}`)
          return {
            platformAccount: kol.platformAccount,
            kol: {
              ...kol,
              instagramUser: {
                ...kol.instagramUser,
                followerCount: Number(kol.instagramUser.followerCount),
                averageLikeCount: Number(kol.instagramUser.averageLikeCount),
                averageCommentCount: Number(kol.instagramUser.averageCommentCount),
                lastPublishedTime: Number(kol.instagramUser.lastPublishedTime),
                posts: posts.sort((a, b) => b.created_at - a.created_at),
              },
            },
          }
        }
        return null
      })

      const processedKols = await Promise.all(kolPromises)
      const filteredKols = processedKols.filter((kol) => kol !== null)
      const kolMap = filteredKols.reduce(
        (acc, { platformAccount, kol }) => {
          acc[platformAccount] = kol
          return acc
        },
        {} as Record<string, any>,
      )

      const result = candidatesData
        .filter((c: ProjectCandidate) => kolMap[c.platformId])
        .map((candidate: ProjectCandidate) => {
          return {
            ...kolMap[candidate.platformId],
            score: candidate.score,
          } as KolInfoWithScore
        })

      // 合并 meta 信息并返回分页结果
      const meta = (projectCandidate.meta as any) || {}
      return {
        data: result,
        total,
        page,
        pageSize,
        totalPages,
        hasMore: meta.hasMore,
        updatedAt: meta.updatedAt,
        progress: meta.progress,
      }
    }
    case KolPlatform.TIKTOK: {
      const kols = await prisma.kolInfo.findMany({
        where: {
          platformAccount: { in: candidatesData.map((c: any) => c.platformId) },
          platform: KolPlatform.TIKTOK,
        },
        include: {
          tiktokUser: {
            omit: {
              videos: false,
            },
          },
          ProjectKol: {
            where: {
              projectId: projectId,
            },
            select: {
              attitude: true,
            },
          },
        },
      })
      const kolPromises = kols.map(async (kol: any) => {
        if (kol.tiktokUser) {
          let convertedVideos: TtVideoBasicInfo[] = []

          if (
            kol.tiktokUser.videos &&
            Array.isArray(kol.tiktokUser.videos) &&
            kol.tiktokUser.videos.length > 0
          ) {
            convertedVideos = kol.tiktokUser.videos as TtVideoBasicInfo[]
          } else {
            const videos = await TiktokApi.getInstance().getUserVideos({
              unique_id: kol.tiktokUser.uniqueId,
              count: 15,
              cursor: 0,
            })
            convertedVideos =
              videos?.map((video: any) => ({
                videoId: video.video_id,
                region: video.region,
                title: video.title,
                cover: video.cover,
                ai_dynamic_cover: video.ai_dynamic_cover,
                origin_cover: video.origin_cover,
                play: video.play,
                wmplay: video.wmplay,
                size: video.size,
                play_count: video.play_count,
                digg_count: video.digg_count,
                comment_count: video.comment_count,
                share_count: video.share_count,
                download_count: video.download_count,
                collect_count: video.collect_count,
                create_time: video.create_time,
                is_ad: video.is_ad,
              })) || []

            if (convertedVideos.length > 0) {
              try {
                await prisma.tikTokUserInfo.update({
                  where: { userId: kol.tiktokUser.userId },
                  data: { videos: convertedVideos as any },
                })
              } catch (error) {
                console.error(`更新用户视频数据失败: ${kol.tiktokUser.uniqueId}`, error)
                Sentry.captureException(error)
              }
            }
          }

          return {
            platformAccount: kol.platformAccount,
            kol: {
              ...kol,
              tiktokUser: {
                ...kol.tiktokUser,
                followerCount: Number(kol.tiktokUser.followerCount),
                lastPublishedTime: Number(kol.tiktokUser.lastPublishedTime),
                averagePlayCount: Number(kol.tiktokUser.averagePlayCount),
                videos: convertedVideos.sort((a, b) => b.create_time - a.create_time),
              },
            },
          }
        }
        return null
      })

      const processedKols = await Promise.all(kolPromises)
      const filteredKols = processedKols.filter((kol) => kol !== null)
      const kolMap = filteredKols.reduce((acc, { platformAccount, kol }) => {
        acc[platformAccount] = kol
        return acc
      }, {} as any)

      const result: KolInfoWithScore[] = candidatesData
        .filter((c: ProjectCandidate) => c.platformId in kolMap)
        .map((candidate: ProjectCandidate) => {
          return {
            ...kolMap[candidate.platformId],
            score: candidate.score,
          } as KolInfoWithScore
        })

      const meta = (projectCandidate.meta as any) || {}
      return {
        data: result,
        total,
        page,
        pageSize,
        totalPages,
        hasMore: meta.hasMore,
        updatedAt: meta.updatedAt,
        progress: meta.progress,
      }
    }
    case KolPlatform.YOUTUBE: {
      const kols = await prisma.kolInfo.findMany({
        where: {
          platformAccount: { in: candidatesData.map((c: any) => c.platformId) },
          platform: KolPlatform.YOUTUBE,
        },
        include: {
          youtubeChannel: true,
          ProjectKol: {
            where: {
              projectId: projectId,
            },
            select: {
              attitude: true,
            },
          },
        },
      })
      const kolPromises = kols.map(async (kol) => {
        if (kol.youtubeChannel) {
          let convertedVideos: ChannelVideo[] = []
          const channel = kol.youtubeChannel as YouTubeChannel
          if (
            (Array.isArray(channel.videos) && channel.videos.length > 0) || // 正常的有视频
            (typeof channel.videos === 'object' &&
              channel.videos !== null &&
              !Array.isArray(channel.videos) &&
              Object.keys(channel.videos).length === 0) // 在任务中正确得到了 videos 确实是空对象，则不用再白找一次视频了
          ) {
            convertedVideos = channel.videos as ChannelVideo[]
          } else {
            let videos: ChannelVideo[] = []
            try {
              console.log(`再次获取用户视频数据: ${kol.youtubeChannel.channelId}`)
              videos = await YoutubeApi.getInstance().getChannelVideos(kol.youtubeChannel.channelId)
            } catch (err) {
              console.error(`获取用户视频数据失败: ${kol.youtubeChannel.channelId}`, err)
            }
            convertedVideos = videos as ChannelVideo[]
            if (convertedVideos.length > 0) {
              try {
                await prisma.youTubeChannel.update({
                  where: { channelId: kol.youtubeChannel.channelId },
                  data: { videos: convertedVideos as any },
                })
              } catch (error) {
                console.error(`更新用户视频数据失败: ${kol.youtubeChannel.channelId}`, error)
                Sentry.captureException(error)
              }
            }
          }

          return {
            platformAccount: kol.platformAccount,
            kol: {
              ...kol,
              youtubeChannel: {
                ...kol.youtubeChannel,
                numericSubscriberCount: Number(kol.youtubeChannel.numericSubscriberCount),
                lastPublishedTime: Number(kol.youtubeChannel.lastPublishedTime),
                videosAverageViewCount: Number(kol.youtubeChannel.videosAverageViewCount),

                videos: convertedVideos,
              },
            },
          }
        }
        return null
      })

      const processedKols = await Promise.all(kolPromises)
      const filteredKols = processedKols.filter((kol) => kol !== null)
      const kolMap = filteredKols.reduce((acc, { platformAccount, kol }) => {
        if (platformAccount) {
          acc[platformAccount] = kol
        }
        return acc
      }, {} as any)

      const result: KolInfoWithScore[] = candidatesData
        .filter((c: ProjectCandidate) => kolMap[c.platformId])
        .map((candidate: ProjectCandidate) => {
          return {
            ...kolMap[candidate.platformId],
            score: candidate.score,
          } as KolInfoWithScore
        })

      const meta = (projectCandidate.meta as any) || {}
      return {
        data: result,
        total,
        page,
        pageSize,
        totalPages,
        hasMore: meta.hasMore,
        updatedAt: meta.updatedAt,
        progress: meta.progress,
      }
    }
    default:
      throw new Error(`不支持的平台类型: ${platform}`)
  }
}

async function handleFollowingListCase(
  projectId: string,
  platform: KolPlatform,
  options?: { page?: number; pageSize?: number },
) {
  console.log(`处理 FOLLOWING_LIST 类型的任务查询，项目ID: ${projectId}`)
  // 从 ProjectCandidate 表中获取数据
  const projectCandidate = await prisma.projectCandidate.findUnique({
    where: {
      projectId_type: {
        projectId: projectId,
        type: TaskType.FOLLOWING_LIST,
      },
    },
  })
  if (!projectCandidate || !projectCandidate.candidates) {
    console.log(`未找到项目 ${projectId} 的 following list 任务结果`)
    return {
      data: [],
      total: 0,
      page: options?.page || 1,
      pageSize: options?.pageSize || 100,
      totalPages: 0,
      hasMore: false,
      progress: {
        total: 0,
        current: 0,
        count: 0,
      },
    }
  }

  const { page, pageSize, skip } = PaginationService.handlePagination(options)
  const allKols = (projectCandidate.candidates as any)?.kols || []
  const total = allKols.length
  const totalPages = Math.ceil(total / pageSize)

  // slice分页取数据
  const candidatesData = allKols.slice(skip, skip + pageSize) || []
  if (candidatesData.length === 0) {
    const meta = (projectCandidate.meta as any) || {}
    return {
      data: [],
      total,
      page,
      pageSize,
      totalPages,
      hasMore: false,
      updatedAt: meta.updatedAt,
      progress: meta.progress,
    }
  }

  switch (platform as KolPlatform) {
    case KolPlatform.INSTAGRAM: {
      console.log('开始处理 Instagram 数据')
      const kols = await prisma.kolInfo.findMany({
        where: {
          platformAccount: { in: candidatesData.map((c: ProjectCandidate) => c.platformId) },
          platform: KolPlatform.INSTAGRAM,
        },
        include: {
          instagramUser: {
            omit: {
              posts: false,
            },
          },
          ProjectKol: {
            where: {
              projectId: projectId,
            },
            select: {
              attitude: true,
            },
          },
        },
      })
      console.log(`找到 ${kols.length} 个 Instagram 用户`)

      const kolPromises = kols.map(async (kol: any) => {
        if (kol.instagramUser) {
          let posts: IgPost[] = []
          if (kol.instagramUser.posts && Array.isArray(kol.instagramUser.posts)) {
            console.log(
              `用户 ${kol.instagramUser.username} 有 ${kol.instagramUser.posts.length} 个帖子`,
            )
            posts = kol.instagramUser.posts.map((post: any) => ({
              ...post,
              id: post.id ? String(post.id) : post.id,
              comment_count: post.comment_count ? Number(post.comment_count) : 0,
              like_count: post.like_count ? Number(post.like_count) : 0,
              play_count: post.play_count ? Number(post.play_count) : 0,
              created_at: post.created_at ? Number(post.created_at) : 0,
            }))
          } else {
            console.log(`用户 ${kol.instagramUser.username} 没有缓存的帖子数据，尝试从 API 获取`)
            const userPosts = await InstagramApi.getInstance().getPosts(kol.instagramUser.username)
            console.log(`从 API 获取到 ${userPosts?.length || 0} 个帖子`)
            posts =
              userPosts?.map((post: any) => ({
                ...post,
                id: post.id ? String(post.id) : post.id,
                comment_count: post.comment_count ? Number(post.comment_count) : 0,
                like_count: post.like_count ? Number(post.like_count) : 0,
                play_count: post.play_count ? Number(post.play_count) : 0,
                created_at: post.created_at ? Number(post.created_at) : 0,
              })) || []

            if (posts.length > 0) {
              console.log(`准备更新用户 ${kol.instagramUser.username} 的帖子数据到数据库`)
              try {
                await prisma.instagramUserInfo.update({
                  where: { username: kol.instagramUser.username },
                  data: {
                    posts: posts,
                    updatedAt: new Date(),
                  },
                })
                console.log('数据库更新成功')
              } catch (error) {
                console.error(`更新Instagram用户帖子数据失败: ${kol.instagramUser.username}`, error)
                Sentry.captureException(error)
              }
            }
          }

          console.log(`最终处理完成的帖子数量: ${posts.length}`)
          return {
            platformAccount: kol.platformAccount,
            kol: {
              ...kol,
              instagramUser: {
                ...kol.instagramUser,
                followerCount: Number(kol.instagramUser.followerCount),
                averageLikeCount: Number(kol.instagramUser.averageLikeCount),
                averageCommentCount: Number(kol.instagramUser.averageCommentCount),
                lastPublishedTime: Number(kol.instagramUser.lastPublishedTime),
                posts: posts.sort((a, b) => b.created_at - a.created_at),
              },
            },
          }
        }
        return null
      })

      const processedKols = await Promise.all(kolPromises)
      const filteredKols = processedKols.filter((kol) => kol !== null)
      const kolMap = filteredKols.reduce(
        (acc, { platformAccount, kol }) => {
          acc[platformAccount] = kol
          return acc
        },
        {} as Record<string, any>,
      )

      const result = candidatesData
        .filter((c: ProjectCandidate) => kolMap[c.platformId])
        .map((candidate: ProjectCandidate) => {
          return {
            ...kolMap[candidate.platformId],
            score: candidate.score,
          } as KolInfoWithScore
        })

      // 合并 meta 信息并返回分页结果
      const meta = (projectCandidate.meta as any) || {}
      return {
        data: result,
        total,
        page,
        pageSize,
        totalPages,
        hasMore: meta.hasMore,
        updatedAt: meta.updatedAt,
        progress: meta.progress,
      }
    }
    case KolPlatform.TIKTOK: {
      const kols = await prisma.kolInfo.findMany({
        where: {
          platformAccount: { in: candidatesData.map((c: any) => c.platformId) },
          platform: KolPlatform.TIKTOK,
        },
        include: {
          tiktokUser: {
            omit: {
              videos: false,
            },
          },
          ProjectKol: {
            where: {
              projectId: projectId,
            },
            select: {
              attitude: true,
            },
          },
        },
      })
      const kolPromises = kols.map(async (kol: any) => {
        if (kol.tiktokUser) {
          let convertedVideos: TtVideoBasicInfo[] = []

          if (
            kol.tiktokUser.videos &&
            Array.isArray(kol.tiktokUser.videos) &&
            kol.tiktokUser.videos.length > 0
          ) {
            convertedVideos = kol.tiktokUser.videos as TtVideoBasicInfo[]
          } else {
            const videos = await TiktokApi.getInstance().getUserVideos({
              unique_id: kol.tiktokUser.uniqueId,
              count: 15,
              cursor: 0,
            })
            convertedVideos =
              videos?.map((video: any) => ({
                videoId: video.video_id,
                region: video.region,
                title: video.title,
                cover: video.cover,
                ai_dynamic_cover: video.ai_dynamic_cover,
                origin_cover: video.origin_cover,
                play: video.play,
                wmplay: video.wmplay,
                size: video.size,
                play_count: video.play_count,
                digg_count: video.digg_count,
                comment_count: video.comment_count,
                share_count: video.share_count,
                download_count: video.download_count,
                collect_count: video.collect_count,
                create_time: video.create_time,
                is_ad: video.is_ad,
              })) || []

            if (convertedVideos.length > 0) {
              try {
                await prisma.tikTokUserInfo.update({
                  where: { userId: kol.tiktokUser.userId },
                  data: { videos: convertedVideos as any },
                })
              } catch (error) {
                console.error(`更新用户视频数据失败: ${kol.tiktokUser.uniqueId}`, error)
                Sentry.captureException(error)
              }
            }
          }

          return {
            platformAccount: kol.platformAccount,
            kol: {
              ...kol,
              tiktokUser: {
                ...kol.tiktokUser,
                followerCount: Number(kol.tiktokUser.followerCount),
                lastPublishedTime: Number(kol.tiktokUser.lastPublishedTime),
                averagePlayCount: Number(kol.tiktokUser.averagePlayCount),
                videos: convertedVideos.sort((a, b) => b.create_time - a.create_time),
              },
            },
          }
        }
        return null
      })

      const processedKols = await Promise.all(kolPromises)
      const filteredKols = processedKols.filter((kol) => kol !== null)
      const kolMap = filteredKols.reduce((acc, { platformAccount, kol }) => {
        acc[platformAccount] = kol
        return acc
      }, {} as any)

      const result: KolInfoWithScore[] = candidatesData
        .filter((c: any) => kolMap[c.platformId])
        .map((candidate: ProjectCandidate) => {
          return {
            ...kolMap[candidate.platformId],
            score: candidate.score,
          } as KolInfoWithScore
        })

      const meta = (projectCandidate.meta as any) || {}
      return {
        data: result,
        total,
        page,
        pageSize,
        totalPages,
        hasMore: meta.hasMore,
        updatedAt: meta.updatedAt,
        progress: meta.progress,
      }
    }
    default:
      throw new Error(`不支持的平台类型: ${platform}`)
  }
}

async function handleBgmBreakCase(
  projectId: string,
  platform: KolPlatform,
  options?: { page?: number; pageSize?: number },
): Promise<BreakTaskSearchResponse> {
  console.log(`处理 BGM_BREAK 类型的任务查询，项目ID: ${projectId}`)
  const projectCandidate = await prisma.projectCandidate.findUnique({
    where: {
      projectId_type: {
        projectId: projectId,
        type: TaskType.BGM_BREAK,
      },
    },
  })

  if (!projectCandidate || !projectCandidate.candidates) {
    console.log(`未找到项目 ${projectId} 的 BGM_BREAK 任务结果`)
    return {
      data: [],
      total: 0,
      page: options?.page || 1,
      pageSize: options?.pageSize || 100,
      totalPages: 0,
      hasMore: false,
    }
  }

  const { page, pageSize, skip } = PaginationService.handlePagination(options)
  const allKols = (projectCandidate.candidates as any)?.kols || []
  const total = allKols.length
  const totalPages = Math.ceil(total / pageSize)
  const meta = (projectCandidate.meta as any) || {}

  // slice分页取数据
  const candidatesData = allKols.slice(skip, skip + pageSize) || []
  if (candidatesData.length === 0) {
    return {
      data: [],
      total,
      page,
      pageSize,
      totalPages,
      hasMore: false,
      updatedAt: meta.updatedAt,
    }
  }

  // BGM_BREAK目前只支持TikTok平台
  if (platform !== KolPlatform.TIKTOK) {
    throw new Error(`BGM_BREAK 不支持的平台类型: ${platform}`)
  }

  const kols = await prisma.kolInfo.findMany({
    where: {
      platformAccount: { in: candidatesData.map((c: any) => c.platformId) },
      platform: KolPlatform.TIKTOK,
    },
    include: {
      tiktokUser: {
        omit: {
          videos: false,
        },
      },
      ProjectKol: {
        where: {
          projectId: projectId,
        },
        select: {
          attitude: true,
        },
      },
    },
  })

  const kolPromises = kols.map(async (kol: any) => {
    if (kol.tiktokUser) {
      let convertedVideos: TtVideoBasicInfo[] = []

      if (
        kol.tiktokUser.videos &&
        Array.isArray(kol.tiktokUser.videos) &&
        kol.tiktokUser.videos.length > 0
      ) {
        convertedVideos = kol.tiktokUser.videos as TtVideoBasicInfo[]
      } else {
        const videos = await TiktokApi.getInstance().getUserVideos({
          unique_id: kol.tiktokUser.uniqueId,
          count: 15,
          cursor: 0,
        })
        convertedVideos =
          videos?.map((video: any) => ({
            videoId: video.video_id,
            region: video.region,
            title: video.title,
            cover: video.cover,
            ai_dynamic_cover: video.ai_dynamic_cover,
            origin_cover: video.origin_cover,
            play: video.play,
            wmplay: video.wmplay,
            size: video.size,
            play_count: video.play_count,
            digg_count: video.digg_count,
            comment_count: video.comment_count,
            share_count: video.share_count,
            download_count: video.download_count,
            collect_count: video.collect_count,
            create_time: video.create_time,
            is_ad: video.is_ad,
            is_top: video.is_top,
            is_original: video.is_original,
            is_music: video.is_music,
            is_video: video.is_video,
          })) || []

        if (convertedVideos.length > 0) {
          try {
            await prisma.tikTokUserInfo.update({
              where: { userId: kol.tiktokUser.userId },
              data: { videos: convertedVideos as any },
            })
          } catch (error) {
            console.error(`更新用户视频数据失败: ${kol.tiktokUser.uniqueId}`, error)
            Sentry.captureException(error)
          }
        }
      }

      return {
        platformAccount: kol.platformAccount,
        kol: {
          ...kol,
          tiktokUser: {
            ...kol.tiktokUser,
            followerCount: Number(kol.tiktokUser.followerCount),
            lastPublishedTime: Number(kol.tiktokUser.lastPublishedTime),
            averagePlayCount: Number(kol.tiktokUser.averagePlayCount),
            videos: convertedVideos,
          },
        },
      }
    }
    return null
  })

  const processedKols = await Promise.all(kolPromises)
  const filteredKols = processedKols.filter((kol) => kol !== null)
  const kolMap = filteredKols.reduce((acc, { platformAccount, kol }) => {
    acc[platformAccount] = kol
    return acc
  }, {} as any)

  const result = candidatesData
    .filter((c: ProjectCandidate) => kolMap[c.platformId])
    .map((candidate: ProjectCandidate) => {
      return {
        ...kolMap[candidate.platformId],
        score: candidate.score || 0,
      }
    })

  return {
    data: result,
    total,
    page,
    pageSize,
    totalPages,
    hasMore: meta.hasMore,
    updatedAt: meta.updatedAt,
  }
}

async function handleTaggedBreakCase(
  projectId: string,
  platform: KolPlatform,
  options?: { page?: number; pageSize?: number },
): Promise<BreakTaskSearchResponse> {
  console.log(`处理 TAGGED_BREAK 类型的任务查询，项目ID: ${projectId}`)

  const projectCandidate = await prisma.projectCandidate.findUnique({
    where: {
      projectId_type: {
        projectId: projectId,
        type: TaskType.TAGGED_BREAK,
      },
    },
  })

  if (!projectCandidate || !projectCandidate.candidates) {
    console.log(`未找到项目 ${projectId} 的 TAGGED_BREAK 任务结果`)
    return {
      data: [],
      total: 0,
      page: options?.page || 1,
      pageSize: options?.pageSize || 100,
      totalPages: 0,
      hasMore: false,
    }
  }

  const { page, pageSize, skip } = PaginationService.handlePagination(options)
  const allKols = (projectCandidate.candidates as any)?.kols || []
  const total = allKols.length
  const totalPages = Math.ceil(total / pageSize)
  const meta = (projectCandidate.meta as any) || {}

  // slice分页取数据
  const candidatesData = allKols.slice(skip, skip + pageSize) || []
  if (candidatesData.length === 0) {
    return {
      data: [],
      total,
      page,
      pageSize,
      totalPages,
      hasMore: false,
      updatedAt: meta.updatedAt,
    }
  }

  switch (platform) {
    case KolPlatform.INSTAGRAM: {
      console.log('开始处理 Instagram TAGGED_BREAK 数据')
      const kols = await prisma.kolInfo.findMany({
        where: {
          platformAccount: { in: candidatesData.map((c: any) => c.platformId) },
          platform: KolPlatform.INSTAGRAM,
        },
        include: {
          instagramUser: {
            omit: {
              posts: false,
            },
          },
          ProjectKol: {
            where: {
              projectId: projectId,
            },
            select: {
              attitude: true,
            },
          },
        },
      })
      console.log(`找到 ${kols.length} 个 Instagram 用户`)

      const kolPromises = kols.map(async (kol: any) => {
        if (kol.instagramUser) {
          let posts: IgPost[] = []
          if (kol.instagramUser.posts && Array.isArray(kol.instagramUser.posts)) {
            console.log(
              `用户 ${kol.instagramUser.username} 有 ${kol.instagramUser.posts.length} 个帖子`,
            )
            posts = kol.instagramUser.posts.map((post: any) => ({
              ...post,
              id: post.id ? String(post.id) : post.id,
              comment_count: post.comment_count ? Number(post.comment_count) : 0,
              like_count: post.like_count ? Number(post.like_count) : 0,
              play_count: post.play_count ? Number(post.play_count) : 0,
              created_at: post.created_at ? Number(post.created_at) : 0,
            }))
          } else {
            console.log(`用户 ${kol.instagramUser.username} 没有缓存的帖子数据，尝试从 API 获取`)
            const userPosts = await InstagramApi.getInstance().getPosts(kol.instagramUser.username)
            console.log(`从 API 获取到 ${userPosts?.length || 0} 个帖子`)
            posts =
              userPosts?.map((post: any) => ({
                ...post,
                id: post.id ? String(post.id) : post.id,
                comment_count: post.comment_count ? Number(post.comment_count) : 0,
                like_count: post.like_count ? Number(post.like_count) : 0,
                play_count: post.play_count ? Number(post.play_count) : 0,
                created_at: post.created_at ? Number(post.created_at) : 0,
              })) || []

            if (posts.length > 0) {
              console.log(`准备更新用户 ${kol.instagramUser.username} 的帖子数据到数据库`)
              try {
                await prisma.instagramUserInfo.update({
                  where: { username: kol.instagramUser.username },
                  data: {
                    posts: posts,
                    updatedAt: new Date(),
                  },
                })
                console.log('数据库更新成功')
              } catch (error) {
                console.error(`更新Instagram用户帖子数据失败: ${kol.instagramUser.username}`, error)
                Sentry.captureException(error)
              }
            }
          }

          console.log(`最终处理完成的帖子数量: ${posts.length}`)
          return {
            platformAccount: kol.platformAccount,
            kol: {
              ...kol,
              instagramUser: {
                ...kol.instagramUser,
                followerCount: Number(kol.instagramUser.followerCount),
                averageLikeCount: Number(kol.instagramUser.averageLikeCount),
                averageCommentCount: Number(kol.instagramUser.averageCommentCount),
                lastPublishedTime: Number(kol.instagramUser.lastPublishedTime),
                posts: posts.sort((a, b) => b.created_at - a.created_at),
              },
            },
          }
        }
        return null
      })

      const processedKols = await Promise.all(kolPromises)
      const filteredKols = processedKols.filter((kol) => kol !== null)
      const kolMap = filteredKols.reduce(
        (acc, { platformAccount, kol }) => {
          acc[platformAccount] = kol
          return acc
        },
        {} as Record<string, any>,
      )

      const result = candidatesData
        .filter((c: ProjectCandidate) => kolMap[c.platformId])
        .map((candidate: ProjectCandidate) => {
          return {
            ...kolMap[candidate.platformId],
            score: candidate.score || 0,
          }
        })

      return {
        data: result,
        total,
        page,
        pageSize,
        totalPages,
        hasMore: meta.hasMore,
        updatedAt: meta.updatedAt,
        progress: meta.progress,
      }
    }
    default:
      throw new Error(`TAGGED_BREAK 不支持的平台类型: ${platform}`)
  }
}

async function handleInputBreakCase(
  projectId: string,
  platform: KolPlatform,
  options?: { page?: number; pageSize?: number },
): Promise<BreakTaskSearchResponse> {
  console.log(`处理 SEARCH_INPUT_BREAK 类型的任务查询，项目ID: ${projectId}`)
  // 从 ProjectCandidate 表中获取数据
  const projectCandidate = await prisma.projectCandidate.findUnique({
    where: {
      projectId_type: {
        projectId: projectId,
        type: TaskType.SEARCH_INPUT_BREAK,
      },
    },
  })

  if (!projectCandidate || !projectCandidate.candidates) {
    console.log(`未找到项目 ${projectId} 的 SEARCH_INPUT_BREAK 任务结果`)
    return {
      data: [],
      total: 0,
      page: options?.page || 1,
      pageSize: options?.pageSize || 100,
      totalPages: 0,
      hasMore: false,
    }
  }

  const { page, pageSize, skip } = PaginationService.handlePagination(options)
  const allKols = (projectCandidate.candidates as any)?.kols || []
  const total = allKols.length
  const totalPages = Math.ceil(total / pageSize)

  // slice分页取数据
  const candidatesData = allKols.slice(skip, skip + pageSize) || []
  if (candidatesData.length === 0) {
    console.log(`项目 ${projectId} 的 SEARCH_INPUT_BREAK 任务结果中没有候选人`)
    const meta = (projectCandidate.meta as any) || {}
    return {
      data: [],
      total,
      page,
      pageSize,
      totalPages,
      hasMore: false,
      updatedAt: meta.updatedAt,
      progress: meta.progress,
    }
  }

  let processedKols: ({ platformAccount: string; kol: any } | null)[] = []
  switch (platform) {
    case KolPlatform.TIKTOK: {
      const kols = await prisma.kolInfo.findMany({
        where: {
          platformAccount: { in: candidatesData.map((c: any) => c.platformId) },
          platform: KolPlatform.TIKTOK,
        },
        include: {
          tiktokUser: {
            omit: {
              videos: false,
            },
          },
          ProjectKol: {
            where: {
              projectId: projectId,
            },
            select: {
              attitude: true,
            },
          },
        },
      })
      const kolPromises = kols.map(async (kol: any) => {
        if (kol.tiktokUser) {
          let convertedVideos: TtVideoBasicInfo[] = []

          if (
            kol.tiktokUser.videos &&
            Array.isArray(kol.tiktokUser.videos) &&
            kol.tiktokUser.videos.length > 0
          ) {
            convertedVideos = kol.tiktokUser.videos as TtVideoBasicInfo[]
          } else {
            const videos = await TiktokApi.getInstance().getUserVideos({
              unique_id: kol.tiktokUser.uniqueId,
              count: 15,
              cursor: 0,
            })
            convertedVideos =
              videos?.map((video: any) => ({
                videoId: video.video_id,
                region: video.region,
                title: video.title,
                cover: video.cover,
                ai_dynamic_cover: video.ai_dynamic_cover,
                origin_cover: video.origin_cover,
                play: video.play,
                wmplay: video.wmplay,
                size: video.size,
                play_count: video.play_count,
                digg_count: video.digg_count,
                comment_count: video.comment_count,
                share_count: video.share_count,
                download_count: video.download_count,
                collect_count: video.collect_count,
                create_time: video.create_time,
                is_ad: video.is_ad,
              })) || []

            if (convertedVideos.length > 0) {
              try {
                await prisma.tikTokUserInfo.update({
                  where: { userId: kol.tiktokUser.userId },
                  data: { videos: convertedVideos as any },
                })
              } catch (error) {
                console.error(`更新用户视频数据失败: ${kol.tiktokUser.uniqueId}`, error)
                Sentry.captureException(error)
              }
            }
          }

          return {
            platformAccount: kol.platformAccount,
            kol: {
              ...kol,
              tiktokUser: {
                ...kol.tiktokUser,
                followerCount: Number(kol.tiktokUser.followerCount),
                lastPublishedTime: Number(kol.tiktokUser.lastPublishedTime),
                averagePlayCount: Number(kol.tiktokUser.averagePlayCount),
                videos: convertedVideos,
              },
            },
          }
        }
        return null
      })
      processedKols = await Promise.all(kolPromises)
      break
    }
    case KolPlatform.YOUTUBE: {
      const kols = await prisma.kolInfo.findMany({
        where: {
          platformAccount: { in: candidatesData.map((c: any) => c.platformId) },
          platform: KolPlatform.YOUTUBE,
        },
        include: {
          youtubeChannel: {
            omit: {
              videos: false,
            },
          },
          ProjectKol: {
            where: {
              projectId: projectId,
            },
            select: {
              attitude: true,
            },
          },
        },
      })
      const kolPromises = kols.map(async (kol: any) => {
        if (kol.youtubeChannel) {
          let convertedVideos: ChannelVideo[] = []

          if (
            kol.youtubeChannel.videos &&
            Array.isArray(kol.youtubeChannel.videos) &&
            kol.youtubeChannel.videos.length > 0
          ) {
            convertedVideos = kol.youtubeChannel.videos as ChannelVideo[]
          } else {
            try {
              convertedVideos = await YoutubeApi.getInstance().getChannelVideos(
                kol.youtubeChannel.channelId,
              )

              if (convertedVideos.length > 0) {
                try {
                  await prisma.youTubeChannel.update({
                    where: { channelId: kol.youtubeChannel.channelId },
                    data: { videos: convertedVideos as any },
                  })
                } catch (error) {
                  console.error(`更新用户视频数据失败: ${kol.tiktokUser.uniqueId}`, error)
                  Sentry.captureException(error)
                }
              }
            } catch (error) {
              console.error(`获取用户视频数据失败: ${kol.youtubeChannel.channelId}`, error)
              // Sentry.captureException(error)
            }
          }

          return {
            platformAccount: kol.platformAccount,
            kol: {
              ...kol,
              youtubeChannel: {
                ...kol.youtubeChannel,
                numericSubscriberCount: Number(kol.youtubeChannel.numericSubscriberCount),
                lastPublishedTime: Number(kol.youtubeChannel.lastPublishedTime),
                videosAverageViewCount: Number(kol.youtubeChannel.videosAverageViewCount),
                videos: convertedVideos,
              },
            },
          }
        }
        return null
      })
      processedKols = await Promise.all(kolPromises)
      break
    }

    default:
      throw new Error(`SEARCH_INPUT_BREAK 不支持的平台类型: ${platform}`)
  }

  const filteredKols = processedKols.filter((kol) => kol !== null)
  const kolMap = filteredKols.reduce((acc, { platformAccount, kol }) => {
    acc[platformAccount] = kol
    return acc
  }, {} as any)

  const result = candidatesData
    .filter((c: ProjectCandidate) => kolMap[c.platformId])
    .map((candidate: ProjectCandidate) => {
      return {
        ...kolMap[candidate.platformId],
        score: 0,
      }
    })

  // 合并 meta 信息并返回分页结果
  const meta = (projectCandidate.meta as any) || {}
  return {
    data: result,
    total,
    page,
    pageSize,
    totalPages,
    hasMore: meta.hasMore,
    updatedAt: meta.updatedAt,
    progress: meta.progress,
  }
}

async function handleFollowersSimilarCase(
  projectId: string,
  platform: KolPlatform,
  options?: { page?: number; pageSize?: number },
): Promise<any> {
  // 从 ProjectCandidate 表中获取数据,不翻页
  const projectCandidate = await prisma.projectCandidate.findUnique({
    where: {
      projectId_type: {
        projectId: projectId,
        type: TaskType.FOLLOWERS_SIMILAR,
      },
    },
  })

  if (!projectCandidate || !projectCandidate.candidates) {
    console.log(`未找到项目 ${projectId} 的 FOLLOWERS_SIMILAR 任务结果`)
    return {
      data: [],
      total: 0,
      page: options?.page || 1,
      pageSize: options?.pageSize || 100,
      totalPages: 0,
      hasMore: false,
    }
  }
  const allKols = (projectCandidate.candidates as any)?.kols || []

  const kols = await prisma.kolInfo.findMany({
    where: {
      platformAccount: { in: allKols.map((c: any) => c.platformId) },
      platform: KolPlatform.TIKTOK,
    },
    include: {
      tiktokUser: {
        omit: {
          videos: false,
        },
      },
      ProjectKol: {
        where: {
          projectId: projectId,
        },
        select: {
          attitude: true,
        },
      },
    },
  })
  const kolPromises = kols.map(async (kol: any) => {
    if (kol.tiktokUser) {
      let convertedVideos: TtVideoBasicInfo[] = []

      if (
        kol.tiktokUser.videos &&
        Array.isArray(kol.tiktokUser.videos) &&
        kol.tiktokUser.videos.length > 0
      ) {
        convertedVideos = kol.tiktokUser.videos as TtVideoBasicInfo[]
      } else {
        const videos = await TiktokApi.getInstance().getUserVideos({
          unique_id: kol.tiktokUser.uniqueId,
          count: 15,
          cursor: 0,
        })
        convertedVideos =
          videos?.map((video: any) => ({
            videoId: video.video_id,
            region: video.region,
            title: video.title,
            cover: video.cover,
            ai_dynamic_cover: video.ai_dynamic_cover,
            origin_cover: video.origin_cover,
            play: video.play,
            wmplay: video.wmplay,
            size: video.size,
            play_count: video.play_count,
            digg_count: video.digg_count,
            comment_count: video.comment_count,
            share_count: video.share_count,
            download_count: video.download_count,
            collect_count: video.collect_count,
            create_time: video.create_time,
            is_ad: video.is_ad,
          })) || []

        if (convertedVideos.length > 0) {
          try {
            await prisma.tikTokUserInfo.update({
              where: { userId: kol.tiktokUser.userId },
              data: { videos: convertedVideos as any },
            })
          } catch (error) {
            console.error(`更新用户视频数据失败: ${kol.tiktokUser.uniqueId}`, error)
            Sentry.captureException(error)
          }
        }
      }

      return {
        platformAccount: kol.platformAccount,
        kol: {
          ...kol,
          tiktokUser: {
            ...kol.tiktokUser,
            followerCount: Number(kol.tiktokUser.followerCount),
            lastPublishedTime: Number(kol.tiktokUser.lastPublishedTime),
            averagePlayCount: Number(kol.tiktokUser.averagePlayCount),
            videos: convertedVideos,
          },
        },
      }
    }
    return null
  })

  const processedKols = await Promise.all(kolPromises)
  const filteredKols = processedKols.filter((kol) => kol !== null)
  const kolMap = filteredKols.reduce((acc, { platformAccount, kol }) => {
    acc[platformAccount] = kol
    return acc
  }, {} as any)

  const result = allKols
    .filter((c: ProjectCandidate) => kolMap[c.platformId])
    .map((candidate: ProjectCandidate) => {
      return {
        ...kolMap[candidate.platformId],
        score: 0,
      }
    })

  return {
    data: result,
    total: allKols.length,
  }
}

export async function getProjectCandidatesWithPostsByTaskType<T>(
  projectId: string,
  taskType: TaskType,
  platform: KolPlatform,
  options?: { page?: number; pageSize?: number },
): Promise<T> {
  const project = await prisma.project.findUnique({
    where: { id: projectId },
    omit: {
      candidates: false,
    },
  })
  if (!project) {
    throw new Error('project not found')
  }

  switch (taskType) {
    case TaskType.HASH_TAG_BREAK:
      return handleHashTagBreakCase(projectId, platform, options) as T
    case TaskType.FOLLOWING_LIST:
      return handleFollowingListCase(projectId, platform, options) as T
    case TaskType.BGM_BREAK:
      return handleBgmBreakCase(projectId, platform, options) as T
    case TaskType.WEB_LIST:
      return handleWebListCase(projectId, platform, options) as T
    case TaskType.TAGGED_BREAK:
      return handleTaggedBreakCase(projectId, platform, options) as T
    case TaskType.SEARCH_INPUT_BREAK:
      return handleInputBreakCase(projectId, platform, options) as T
    // case TaskType.FOLLOWERS_SIMILAR:
    //   return handleFollowersSimilarCase(projectId, platform, options)
    case TaskType.SIMILAR: {
      // 处理其他类型的任务（原有逻辑）
      const project = await prisma.project.findUnique({
        where: { id: projectId },
        omit: {
          candidates: false,
        },
      })
      if (!project) {
        throw new Error('project not found')
      }
      const candidates = (project.candidates as unknown as ProjectCandidates)?.[taskType]
      if (!candidates || !candidates.kols || !candidates.kols.length) {
        // 如果候选列表为空，则返回空数组
        return [] as T
      }
      const kolIds = await filterUnreadKolIds(
        project.id,
        candidates.kols.map((k) => k.kolId),
      )
      const unreadCandidates = candidates.kols
        .filter((k) => kolIds.includes(k.kolId))
        .sort((a, b) => {
          //TODO: remove this after score is fixed
          if ((a.score as any)?.score) {
            return (b.score as any)?.score - (a.score as any)?.score
          }
          return b.score - a.score
        })
      let query: any = {}
      query = {
        platformAccount: { in: unreadCandidates.map((c) => c.platformId) },
        platform,
      }

      const kols = await prisma.kolInfo.findMany({
        where: query,
        include: {
          tiktokUser: {
            omit: {
              videos: false,
            },
          },
          youtubeChannel: {
            omit: {
              videos: false,
            },
          },
          instagramUser: {
            omit: {
              posts: false,
            },
          },
          ProjectKol: {
            where: {
              projectId: projectId,
            },
            select: {
              attitude: true,
            },
          },
        },
      })

      // 收集所有异步操作
      const kolPromises = kols.map(async (kol: any) => {
        if (kol.tiktokUser) {
          let convertedVideos: TtVideoBasicInfo[] = (kol?.tiktokUser?.videos ??
            []) as TtVideoBasicInfo[]

          if (
            !kol.tiktokUser.videos ||
            !Array.isArray(kol.tiktokUser.videos) ||
            kol.tiktokUser.videos.length == 0
          ) {
            console.log(`数据库中没有视频数据，调用第三方 API: ${kol.tiktokUser.uniqueId}`)
            const videos = await TiktokApi.getInstance().getUserVideos({
              unique_id: kol.tiktokUser.uniqueId,
              count: 15,
              cursor: 0,
            })
            convertedVideos =
              videos?.map((video) => ({
                videoId: video.video_id,
                region: video.region,
                title: video.title,
                cover: video.cover,
                ai_dynamic_cover: video.ai_dynamic_cover,
                origin_cover: video.origin_cover,
                play: video.play,
                wmplay: video.wmplay,
                size: video.size,
                play_count: video.play_count,
                digg_count: video.digg_count,
                comment_count: video.comment_count,
                share_count: video.share_count,
                download_count: video.download_count,
                collect_count: video.collect_count,
                create_time: video.create_time,
                is_ad: video.is_ad,
              })) || []

            if (convertedVideos.length > 0) {
              try {
                await prisma.tikTokUserInfo.update({
                  where: { userId: kol.tiktokUser.userId },
                  data: { videos: convertedVideos as any },
                })
                console.log(
                  `更新用户视频数据到数据库: ${kol.tiktokUser.uniqueId}, 视频数量: ${convertedVideos.length}`,
                )
              } catch (error) {
                console.error(`更新用户视频数据失败: ${kol.tiktokUser.uniqueId}`, error)
                Sentry.captureException(error)
              }
            }
          }

          return {
            platformAccount: kol.platformAccount,
            kol: {
              ...kol,
              tiktokUser: {
                ...kol.tiktokUser,
                // followerCount: Number(kol.tiktokUser.followerCount),
                // lastPublishedTime: Number(kol.tiktokUser.lastPublishedTime),
                // averagePlayCount: Number(kol.tiktokUser.averagePlayCount),
                videos: convertedVideos.sort((a, b) => b.create_time - a.create_time),
              },
            },
          }
        } else if (kol.youtubeChannel) {
          const channel = kol.youtubeChannel as YouTubeChannel
          let convertedVideos: ChannelVideo[] = (channel.videos ?? []) as ChannelVideo[]
          if (!channel.videos || !Array.isArray(channel.videos) || channel.videos.length == 0) {
            const videos = await YoutubeApi.getInstance().getChannelVideos(kol.platformAccount)
            convertedVideos =
              videos?.map((video: any) => ({
                type: video.type,
                title: video.title,
                videoId: video.videoId,
                lengthText: video.lengthText,
                viewCount: video.viewCount,
                publishedTimeText: video.publishedTimeText,
                thumbnail: video.thumbnail[video.thumbnail.length - 1].url,
                description: video.description,
                richThumbnail: video.richThumbnail,
                channelId: video.channelId,
                publishedAt: video.publishedAt,
              })) || []
          }
          return {
            platformAccount: kol.platformAccount,
            kol: {
              ...kol,
              youtubeChannel: {
                thumbnail: kol.avatar,
                title: channel.channelHandle,
                description: channel.channelDescription,
                region: channel.country,
                numericSubscriberCount: Number(kol.youtubeChannel.numericSubscriberCount),
                videosAverageViewCount: Number(kol.youtubeChannel.videosAverageViewCount),
                lastPublishedTime: Number(kol.youtubeChannel.lastPublishedTime),
                videos: convertedVideos,
              },
            },
          }
        } else if (kol.instagramUser) {
          let convertedPosts: IgPost[] = (kol?.instagramUser?.posts ?? []) as IgPost[]
          if (
            !kol.instagramUser.posts ||
            !Array.isArray(kol.instagramUser.posts) ||
            kol.instagramUser.posts.length == 0
          ) {
            const posts = await InstagramApi.getInstance().getPosts(kol.platformAccount)
            convertedPosts =
              posts?.map((post: any) => ({
                ...post,
                id: post.id ? String(post.id) : post.id,
                comment_count: post.comment_count ? Number(post.comment_count) : 0,
                like_count: post.like_count ? Number(post.like_count) : 0,
                play_count: post.play_count ? Number(post.play_count) : 0,
                created_at: post.created_at ? Number(post.created_at) : 0,
              })) || []

            if (convertedPosts.length > 0) {
              await prisma.instagramUserInfo.update({
                where: { username: kol.instagramUser.username },
                data: { posts: convertedPosts },
              })
            }
          }
          return {
            platformAccount: kol.platformAccount,
            kol: {
              ...kol,
              instagramUser: {
                ...kol.instagramUser,
                posts: convertedPosts.sort((a, b) => b.created_at - a.created_at),
              },
            },
          }
        }
        return null
      })

      // 等待所有异步操作完成
      //使用 map 和 Promise.all 来并行处理所有异步请求
      const processedKols = await Promise.all(kolPromises)
      const filteredKols = processedKols.filter((kol) => kol !== null)
      // 构建 kolMap
      const kolMap = filteredKols.reduce((acc, { platformAccount, kol }) => {
        acc[platformAccount] = kol
        return acc
      }, {} as any)

      const result: KolInfoWithScore[] = unreadCandidates
        .filter((c: ProjectCandidate) => kolMap[c.platformId])
        .map((candidate: ProjectCandidate) => {
          return {
            ...kolMap[candidate.platformId],
            score: candidate.score,
          } as KolInfoWithScore
        })
      return result as T
    }
    default:
      console.log(`不支持的任务类型: ${taskType}`)
      throw new Error(`不支持的任务类型: ${taskType}`)
  }
}

async function handleWebListCase(
  projectId: string,
  platform: KolPlatform,
  options?: { page?: number; pageSize?: number },
): Promise<BreakTaskSearchResponse> {
  console.log(`处理 WEB_LIST 类型的任务查询，项目ID: ${projectId}`)
  const projectCandidate = await prisma.projectCandidate.findUnique({
    where: {
      projectId_type: {
        projectId: projectId,
        type: TaskType.WEB_LIST,
      },
    },
  })

  if (!projectCandidate || !projectCandidate.candidates) {
    console.log(`未找到项目 ${projectId} 的 WEB_LIST 任务结果`)
    return {
      data: [],
      total: 0,
      page: options?.page || 1,
      pageSize: options?.pageSize || 100,
      totalPages: 0,
      hasMore: false,
      message: `未找到项目 ${projectId} 的 WEB_LIST 任务结果`,
    }
  }

  const { page, pageSize, skip } = PaginationService.handlePagination(options)
  const allKols = (projectCandidate.candidates as any)?.kols || []
  const total = allKols.length
  const totalPages = Math.ceil(total / pageSize)
  const meta = (projectCandidate.meta as any) || {}

  // slice分页取数据
  const candidatesData = allKols.slice(skip, skip + pageSize) || []
  if (candidatesData.length === 0) {
    return {
      data: [],
      total,
      page,
      pageSize,
      totalPages,
      hasMore: false,
      message: meta.message || 'no candidates found',
    }
  }

  // 根据平台类型处理不同的数据
  switch (platform) {
    case KolPlatform.TIKTOK: {
      const kols = await prisma.kolInfo.findMany({
        where: {
          platformAccount: { in: candidatesData.map((c: any) => c.platformId) },
          platform: KolPlatform.TIKTOK,
        },
        include: {
          tiktokUser: {
            omit: {
              videos: false,
            },
          },
          ProjectKol: {
            where: {
              projectId: projectId,
            },
            select: {
              attitude: true,
            },
          },
        },
      })

      const kolPromises = kols.map(async (kol: any) => {
        if (kol.tiktokUser) {
          let convertedVideos: TtVideoBasicInfo[] = []

          if (
            kol.tiktokUser.videos &&
            Array.isArray(kol.tiktokUser.videos) &&
            kol.tiktokUser.videos.length > 0
          ) {
            convertedVideos = kol.tiktokUser.videos as TtVideoBasicInfo[]
          } else {
            const videos = await TiktokApi.getInstance().getUserVideos({
              unique_id: kol.tiktokUser.uniqueId,
              count: 15,
              cursor: 0,
            })
            convertedVideos =
              videos?.map((video: any) => ({
                videoId: video.video_id,
                region: video.region,
                title: video.title,
                cover: video.cover,
                ai_dynamic_cover: video.ai_dynamic_cover,
                origin_cover: video.origin_cover,
                play: video.play,
                wmplay: video.wmplay,
                size: video.size,
                play_count: video.play_count,
                digg_count: video.digg_count,
                comment_count: video.comment_count,
                share_count: video.share_count,
                download_count: video.download_count,
                collect_count: video.collect_count,
                create_time: video.create_time,
                is_ad: video.is_ad,
                is_top: video.is_top,
                is_original: video.is_original,
                is_music: video.is_music,
                is_video: video.is_video,
              })) || []

            if (convertedVideos.length > 0) {
              try {
                await prisma.tikTokUserInfo.update({
                  where: { userId: kol.tiktokUser.userId },
                  data: { videos: convertedVideos as any },
                })
              } catch (error) {
                console.error(`更新用户视频数据失败: ${kol.tiktokUser.uniqueId}`, error)
                Sentry.captureException(error)
              }
            }
          }

          return {
            platformAccount: kol.platformAccount,
            kol: {
              ...kol,
              tiktokUser: {
                ...kol.tiktokUser,
                followerCount: Number(kol.tiktokUser.followerCount),
                lastPublishedTime: Number(kol.tiktokUser.lastPublishedTime),
                averagePlayCount: Number(kol.tiktokUser.averagePlayCount),
                videos: convertedVideos,
              },
            },
          }
        }
        return null
      })

      const processedKols = await Promise.all(kolPromises)
      const filteredKols = processedKols.filter((kol) => kol !== null)
      const kolMap = filteredKols.reduce((acc, { platformAccount, kol }) => {
        acc[platformAccount] = kol
        return acc
      }, {} as any)

      const result = candidatesData
        .filter((c: ProjectCandidate) => kolMap[c.platformId])
        .map((candidate: ProjectCandidate) => {
          return {
            ...kolMap[candidate.platformId],
            score: candidate.score || 0,
          }
        })

      return {
        data: result,
        total,
        page,
        pageSize,
        totalPages,
        hasMore: page < totalPages,
        message: meta.message || 'success',
      }
    }
    case KolPlatform.INSTAGRAM: {
      console.log('开始处理 Instagram WEB_LIST 数据')
      const kols = await prisma.kolInfo.findMany({
        where: {
          platformAccount: { in: candidatesData.map((c: any) => c.platformId) },
          platform: KolPlatform.INSTAGRAM,
        },
        include: {
          instagramUser: {
            omit: {
              posts: false,
            },
          },
          ProjectKol: {
            where: {
              projectId: projectId,
            },
            select: {
              attitude: true,
            },
          },
        },
      })
      console.log(`找到 ${kols.length} 个 Instagram 用户`)

      const kolPromises = kols.map(async (kol: any) => {
        if (kol.instagramUser) {
          let posts: IgPost[] = []
          if (kol.instagramUser.posts && Array.isArray(kol.instagramUser.posts)) {
            console.log(
              `用户 ${kol.instagramUser.username} 有 ${kol.instagramUser.posts.length} 个帖子`,
            )
            posts = kol.instagramUser.posts.map((post: any) => ({
              ...post,
              id: post.id ? String(post.id) : post.id,
              comment_count: post.comment_count ? Number(post.comment_count) : 0,
              like_count: post.like_count ? Number(post.like_count) : 0,
              play_count: post.play_count ? Number(post.play_count) : 0,
              created_at: post.created_at ? Number(post.created_at) : 0,
            }))
          } else {
            console.log(`用户 ${kol.instagramUser.username} 没有缓存的帖子数据，尝试从 API 获取`)
            const userPosts = await InstagramApi.getInstance().getPosts(kol.instagramUser.username)
            console.log(`从 API 获取到 ${userPosts?.length || 0} 个帖子`)
            posts =
              userPosts?.map((post: any) => ({
                ...post,
                id: post.id ? String(post.id) : post.id,
                comment_count: post.comment_count ? Number(post.comment_count) : 0,
                like_count: post.like_count ? Number(post.like_count) : 0,
                play_count: post.play_count ? Number(post.play_count) : 0,
                created_at: post.created_at ? Number(post.created_at) : 0,
              })) || []

            if (posts.length > 0) {
              console.log(`准备更新用户 ${kol.instagramUser.username} 的帖子数据到数据库`)
              try {
                await prisma.instagramUserInfo.update({
                  where: { username: kol.instagramUser.username },
                  data: {
                    posts: posts,
                    updatedAt: new Date(),
                  },
                })
                console.log('数据库更新成功')
              } catch (error) {
                console.error(`更新Instagram用户帖子数据失败: ${kol.instagramUser.username}`, error)
                Sentry.captureException(error)
              }
            }
          }

          return {
            platformAccount: kol.platformAccount,
            kol: {
              ...kol,
              instagramUser: {
                ...kol.instagramUser,
                followerCount: Number(kol.instagramUser.followerCount),
                averageLikeCount: Number(kol.instagramUser.averageLikeCount),
                averageCommentCount: Number(kol.instagramUser.averageCommentCount),
                lastPublishedTime: Number(kol.instagramUser.lastPublishedTime),
                posts: posts.sort((a, b) => b.created_at - a.created_at),
              },
            },
          }
        }
        return null
      })

      const processedKols = await Promise.all(kolPromises)
      const filteredKols = processedKols.filter((kol) => kol !== null)
      const kolMap = filteredKols.reduce(
        (acc, { platformAccount, kol }) => {
          acc[platformAccount] = kol
          return acc
        },
        {} as Record<string, any>,
      )

      const result = candidatesData
        .filter((c: ProjectCandidate) => kolMap[c.platformId])
        .map((candidate: ProjectCandidate) => {
          return {
            ...kolMap[candidate.platformId],
            score: candidate.score || 0,
          }
        })

      return {
        data: result,
        total,
        page,
        pageSize,
        totalPages,
        hasMore: page < totalPages,
        message: meta.message || 'success',
      }
    }
    default:
      throw new Error(`WEB_LIST 不支持的平台类型: ${platform}`)
  }
}
